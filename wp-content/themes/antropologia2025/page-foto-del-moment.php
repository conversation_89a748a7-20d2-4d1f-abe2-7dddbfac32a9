<?php
/**
 * Template for displaying the "Fotos del Moment" page
 *
 * @package pimpampum
 */

get_header();
?>

<main id="main" role="main" class="fotomoment-archive">


<section class="fotomoment-intro">
<div class="wrap">
      <h1 class="section_title"><?php the_title(); ?></h1>
    <div class="fotomoment-intro-media">


    <?php
    // Query the latest foto_del_moment post
    $latest_args = [
        'post_type' => 'foto_del_moment',
        'posts_per_page' => 1,
        'orderby' => 'date',
        'order' => 'DESC'
    ];

    $latest_query = new WP_Query($latest_args);

    // Variable to store the latest photo ID (to exclude from main gallery)
    $latest_photo_id = 0;

    // Store all the photos data for the modal (we'll add to this later)
    $all_photos = [];

    // We'll need to query all photos for the modal navigation
    $all_photos_args = [
        'post_type' => 'foto_del_moment',
        'posts_per_page' => -1,
        'orderby' => 'date',
        'order' => 'DESC',
        'fields' => 'ids'
    ];

    if ($latest_query->have_posts()) :
        while ($latest_query->have_posts()) : $latest_query->the_post();
            if (has_post_thumbnail()) :
                $latest_photo_id = get_the_ID();
                $thumbnail_id = get_post_thumbnail_id();
                $thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'large');
                $thumbnail_alt = get_post_meta($thumbnail_id, '_wp_attachment_image_alt', true);

                // Get the post content and author
                $content = get_the_content();
                $title = get_the_title();

                // Get the pod for this post to retrieve the autor field
                $pod = pods('foto_del_moment', get_the_ID());
                $author = $pod->field('autor');

                // Store the latest photo data for the modal
                $all_photos[] = [
                    'id' => $latest_photo_id,
                    'title' => $title,
                    'image_url' => $thumbnail_url[0],
                    'image_alt' => $thumbnail_alt,
                    'content' => $content,
                    'author' => $author
                ];
    ?>
                <div class="latest-foto" data-id="<?php echo $latest_photo_id; ?>">
                    <img src="<?php echo esc_url($thumbnail_url[0]); ?>" alt="<?php echo esc_attr($thumbnail_alt); ?>" class="fotomoment-ultima">
                    <h3><?php echo esc_html($title); ?></h3>
                    <p class="fotomoment-autor">
                        <?php echo esc_html($author); ?>
                    </p>
                </div>

    <?php
        endif;
        endwhile;
        wp_reset_postdata();
    else :
        echo '<p>No s\'ha trobat cap foto del moment.</p>';
    endif;
    ?>
</div>

    <div class="fotomoment-intro-text">
        <?php
       // Get the main content of the page
    while (have_posts()) : the_post();
        the_content();
    endwhile;
    ?></div>
        </div>
</section>
    <?php


    // Get current page for pagination
    $paged = isset($_GET['pg']) ? absint($_GET['pg']) : 1;

    // Number of photos per page
    $photos_per_page = 12;

    // Query all foto_del_moment posts except the latest one
    $args = [
        'post_type' => 'foto_del_moment',
        'posts_per_page' => $photos_per_page,
        'orderby' => 'date',
        'order' => 'DESC',
        'post__not_in' => [$latest_photo_id], // Exclude the latest photo
        'paged' => $paged
    ];

    $fotos_query = new WP_Query($args);

    if ($fotos_query->have_posts()) :
    ?>
        <div id="fotos-grid" class="fotos-grid wrap">
            <?php
            while ($fotos_query->have_posts()) : $fotos_query->the_post();
                // Only proceed if the post has a featured image
                if (has_post_thumbnail()) :
                    // Get the featured image
                    $thumbnail_id = get_post_thumbnail_id();
                    $thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'large');
                    $thumbnail_alt = get_post_meta($thumbnail_id, '_wp_attachment_image_alt', true);

                    // Get the post content and author
                    $content = get_the_content();
                    $title = get_the_title();

                    // Get the pod for this post to retrieve the autor field
                    $pod = pods('foto_del_moment', get_the_ID());
                    $author = $pod->field('autor');
                    $data = $pod->field('data');

                    // Store the photo data for the modal
                    $all_photos[] = [
                        'id' => get_the_ID(),
                        'title' => $title,
                        'image_url' => $thumbnail_url[0],
                        'image_alt' => $thumbnail_alt,
                        'content' => $content,
                        'author' => $author,
                        'data' => $data,
                    ];
            ?>
                <div class="foto-item" data-id="<?php echo get_the_ID(); ?>">
                    <div class="foto-inner">
                        <img src="<?php echo esc_url($thumbnail_url[0]); ?>" alt="<?php echo esc_attr($thumbnail_alt); ?>" />
                        <div class="foto-overlay">

                            <h3><?php echo esc_html($title); ?></h3>

                            <p class="foto-author">
                                <?php
                                echo esc_html($author);
                                ?>
                                </p>
                                <?php
                                if (!empty($data)) {
                                    echo '<p>' . esc_html($data).'</p>';
                                }
                                ?>
                         
                        </div>
                    </div>
                </div>


            <?php
                endif;
            endwhile;
            wp_reset_postdata();
            ?>
        </div>

        <div class="pagination-container wrap">
            <?php
            $big = 999999999; // need an unlikely integer

            // Get the current page URL without query parameters
            $current_url = get_permalink();

            echo paginate_links([
                'base' => add_query_arg('pg', '%#%', $current_url) . '#fotos-grid',
                'format' => '',
                'current' => max(1, $paged),
                'total' => $fotos_query->max_num_pages,
                'prev_text' => '&laquo; Anterior',
                'next_text' => 'Següent &raquo;',
                'type' => 'list',
                'end_size' => 1,
                'mid_size' => 2
            ]);
            ?>
        </div>

        <!-- Modal for displaying the photo details -->
        <div id="foto-modal" class="foto-modal">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <div class="modal-inner">
                    <div class="modal-image">
                        <img src="" alt="" id="modal-img">
                    </div>
                    <div class="modal-text">
                        <h2 id="modal-title"></h2>
                        <div id="modal-author" class="modal-author"></div>
                        <div id="modal-content"></div>
                        <?php
                        // Get the pod field "data" for the current photo
                        $photo_pod = pods('foto', get_the_ID());
                        $data_field = $photo_pod->field('data');
                        if (!empty($data_field)) : ?>
                            <div id="modal-data"><?php echo esc_html($data_field); ?></div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-navigation">
                        <button class="nav-prev" aria-label="Previous photo">&#10094;</button>
                        <button class="nav-next" aria-label="Next photo">&#10095;</button>
                    </div>
                </div>
            </div>
        </div>

        <?php
        // Now get all photos for the modal navigation
        $all_photos_ids = get_posts($all_photos_args);

        // Reset all_photos array to include all photos, not just the ones on this page
        $all_photos = [];

        // Add all photos to the array for modal navigation
        foreach ($all_photos_ids as $photo_id) {
            // Skip if this is a duplicate of the latest photo
            if ($photo_id == $latest_photo_id && !empty($all_photos)) {
                continue;
            }

            // Get the featured image
            $thumbnail_id = get_post_thumbnail_id($photo_id);
            if (!$thumbnail_id) continue; // Skip if no thumbnail

            $thumbnail_url = wp_get_attachment_image_src($thumbnail_id, 'large');
            $thumbnail_alt = get_post_meta($thumbnail_id, '_wp_attachment_image_alt', true);

            // Get the post content and author
            $post_obj = get_post($photo_id);
            $content = $post_obj->post_content;
            $title = $post_obj->post_title;

            // Get the pod for this post to retrieve the autor field
            $pod = pods('foto_del_moment', $photo_id);
            $author = $pod->field('autor');
            $data = $pod->field('data');
            // Add to all_photos array
            $all_photos[] = [
                'id' => $photo_id,
                'title' => $title,
                'image_url' => $thumbnail_url[0],
                'image_alt' => $thumbnail_alt,
                'content' => $content,
                'author' => $author
            ];
        }
        ?>

        <script>
            // Make photos data available to the external JS file
            window.allPhotos = <?php echo json_encode($all_photos); ?>;
        </script>

        <?php
        // Enqueue our custom gallery script
        wp_enqueue_script('fotos-gallery', get_template_directory_uri() . '/js/fotos-gallery.js', ['jquery'], '1.0.0', true);
        ?>
    <?php else : ?>
        <p>No s'han trobat fotos del moment.</p>
    <?php endif; ?>
</main>

<style>


/* Grid layout for photos */

/* Photo item styling */
.foto-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius);
    cursor: pointer;
    aspect-ratio: 3 / 2;
}

.foto-inner {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.foto-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.5s ease, opacity 0.3s ease;
    opacity: 0;
}

.foto-item img.loaded {
    opacity: 1;
}

.foto-item:hover img {
    transform: scale(1.1);
}

.foto-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: var(--gapS)  var(--gapXS) var(--gapS) 30px;
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
    transform: translateY(10px);
}

.foto-item:hover .foto-overlay {
    opacity: 1;
    transform: translateY(0);
}

.foto-overlay h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/* Modal styling */
.foto-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    padding: 20px;
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #000;
    border-radius: 12px;
    max-width: 95%;
    max-height: 95vh;
    width: 1600px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 15px 30px rgba(155, 155, 155, 0.3);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.modal-inner {
    display: flex;
    overflow: hidden;
    max-height: 95vh;
    position: relative;
}

.modal-image {
    flex: 1.5;
    min-width: 60%;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    position: relative;
}

.modal-image img {
    max-width: 100%;
    max-height: 85vh;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
}

.modal-text {
    flex: 1;
    padding: var(--gapM) var(--gapXL) var(--gapM) var(--gapM) ;
    overflow-y: auto;
    background-color: #000;
    color: #fff;
    min-width: 300px;
}

.modal-text a,
.modal-text a:visited {
    color: #fff;
    text-decoration: underline;
}


.modal-text h2 {
    margin-top: 0;
    color: #fff;
    font-size: 1.4rem;
    margin-bottom: 10px;

}

.modal-author {
    margin-bottom: 30px;
    font-style: italic;
    color: #fff;


    border-radius: 4px;
    display: inline-block;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 28px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border-radius: 50%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}



.modal-navigation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 5;
}

.nav-prev, .nav-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.6;
    pointer-events: auto;
}

.nav-prev {
    left: 20px;
}

.nav-next {
    right: 20px;
}

.nav-prev:hover, .nav-next:hover {
    background-color: rgba(255, 255, 255, 0.5);
    opacity: 1;
}



@media screen and (max-width: 992px) {
    .modal-inner {
        flex-direction: column;
    }

    .modal-image, .modal-text {
        width: 100%;
    }

    .modal-image {
        height: 60vh;
    }

    .modal-text {
        padding: 20px;
        max-height: 35vh;
        overflow-y: auto;
    }
}

@media screen and (max-width: 768px) {


    .nav-prev, .nav-next {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .nav-prev {
        left: 10px;
    }

    .nav-next {
        right: 10px;
    }

    .modal-content {
        max-width: 100%;
        border-radius: 0;
    }
}

/* Clear fix for the grid */
.fotos-grid:after {
    content: "";
    display: table;
    clear: both;
}

/* Pagination styles */
.pagination-container {
    margin: 40px auto;
    text-align: center;
}

.pagination-container ul {
    display: inline-flex;
    list-style: none;
    padding: 0;
    margin: 0;
    flex-wrap: wrap;
    justify-content: center;
}

.pagination-container li {
    margin: 0 5px 5px;
}

.pagination-container a,
.pagination-container span {
    display: inline-block;
    padding: 8px 14px;
    background-color: #f5f5f5;
    color: #333;
    text-decoration: none;
    border-radius: var(--radius);
    transition: all 0.3s ease;
}

.pagination-container a:hover {
    background-color: #e0e0e0;
}

.pagination-container .current {
    background-color: var(--color);
    color: white;
}

.pagination-container .dots {
    background: none;
}
</style>

<?php get_footer(); ?>