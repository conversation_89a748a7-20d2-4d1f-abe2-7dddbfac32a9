.menu-toggle {
	display: none;
}

@media screen and (max-width: 767px) {
	.main-navigation,
	.menu-toggle,
	.main-navigation.toggled ul {
		display: block;
	}

	.main-navigation ul {
		display: none;
		font-family: var(--headerfont);
		list-style: none;
		margin: 0 auto;
		padding-left: 0;
		padding-top: 1em;
		position: relative;
	}

	button.menu-toggle {
		background-color: var(--bodytext);
		color: #fff;
		border: none;
		border-radius: 0;
		font-family: sans-serif;
		padding: 5px 10px;
		margin: 0;
		width: 40px;
		font-size: 1.2rem;
		overflow: hidden;
		position: absolute;
		top: 20px;
		right: 20px;
	}

	#menu-principal a {
		text-decoration: none;
		font-size: 1rem;
		padding: 10px 5px 10px 0;
		display: block;
		color: var(--bodytext);
		border-bottom: 1px solid #dedcd3;
	}

	#menu-principal a:hover {
		background-color: var(--bgpage);
	}

	#menu-principal > li > a {
		font-weight: 600;
	}

	#menu-principal li li a,
	#menu-principal li li a:visited {
		font-weight: 400;
	}

	#menu-principal li li a:before {
		content: "- ";
	}

	#menu-principal li li li a:before {
		content: "-- ";
	}

	#menu-principal li {
		font-size: 1.0625rem;
		line-height: 1.3;
		position: relative;
	}

	.open {
		position: absolute;
		right: 10px;
		top: 11px;
		color: #fff;
		font-weight: 500;
		border-radius: 50%;
		width: 24px;
		height: 24px;
		text-align: center;
		line-height: 24px;
	}

	.search-menu {
		padding: 10px 5px 30px 30px;
	}
}

/* Menú superior preheader */

.upper-navigation {
	position: absolute;
	top: 15px;
	right: 70px;
}

.upper-navigation ul#menu-superior {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-bottom: 0;
	margin-left: 0;
	padding-bottom: 10px;
	padding-left: 0;
}

.upper-navigation li {
	flex: auto;
	margin: 0 0 0 15px;
	padding-top: 0;
	list-style-type: none;
	font-size: 0.75rem;
}

.upper-navigation li a {
	text-decoration: none;
	font-weight: 600;
}

.upper-navigation .ico a {
	width: 20px;
	height: 20px;
	display: block;
	text-indent: -999em;
	background-repeat: no-repeat;
	background-position: center center;
}

.upper-navigation .ico_twitter a {
	background-image: url(../img/ico_twitter_black.svg);
}

.upper-navigation .ico_twitter a {
	background-image: url(../img/ico_bluesky_black.svg);
}

.upper-navigation .ico_instagram a {
	background-image: url(../img/ico_instagram_black.svg);
}

.upper-navigation .ico_facebook a {
	background-image: url(../img/ico_facebook_black.svg);
}

.upper-navigation .ico_youtube a {
	background-image: url(../img/ico_youtube_black.svg);
}

.upper-navigation #searchform input {
	padding: 8px 12px 6px 12px;
	line-height: 1;
	margin-left: 0;
	width: 30px;
	font-size: 0.75rem;
	color: #999;
	background: url(../img/ico_lupa.svg) no-repeat calc(100% - 10px) center;
	border: var(--border);
	text-indent: -999em;
}

.upper-navigation #searchform input:focus {
	border: var(--border);
	color: #000;
	border-color: #000;
	width: 150px;
	position: absolute;
	right: 0;
	top: 0;
	background-color: #fff;
	text-indent: 0;
}

.main-navigation.toggled ul#menu-principal > li > a {
	font-weight: 700;
}

.menu-item-gtranslate {
	position: relative;
}

.menu-item-gtranslate > a {
	cursor: pointer;
	position: relative;
}

.menu-item-gtranslate > a::after {
	content: url(../img/arrow_down_black2.svg);
	margin-left: 8px;
}

.menu-item-gtranslate:hover ul.sub-menu,
.menu-item-gtranslate:focus-within ul.sub-menu {
	display: block;
}

.menu-item-gtranslate ul.sub-menu {
	display: none;
	position: absolute;
	top: 100%;
	left: -20px;
	background-color: #fff;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	padding: 10px 0;
	z-index: 10;
	margin-left: 0;
}

.menu-item-gtranslate ul.sub-menu li {
	list-style: none;
	padding: 5px 20px;
	margin: 0;
}

.menu-item-gtranslate ul.sub-menu li a {
	text-decoration: none;
	color: var(--bodytext);
	display: block;
}

.menu-item-gtranslate ul.sub-menu li a:hover {
	color: var(--accent);
	background-color: var(--grisfons);
}

/* VERSIONS GRANS */

@media (min-width: 768px) {
	.menu-toggle,
	.main-navigation li.mobil,
	.main-navigation li.search-menu,
	.menu-item-home {
		display: none;
	}

	#menu-principal {
		display: flex;
		justify-content: space-between;
		gap: 0.75em;
		position: relative;
		margin: 0;
		padding-left: 0;
	}

	.main-navigation {
		border-top: var(--border);
		margin-top: 20px;
		padding-top: 12px;
		flex: 1;
	}

	.main-navigation li {
		font-weight: 400;
		font-family: var(--headerfont);
		margin: 0;
		padding-top: 0;
		list-style-type: none;
		line-height: 1;
	}

	.main-navigation #menu-principal > li:first-of-type {
		display: none;
	}

	.main-navigation a {
		display: block;
		font-size: 0.875rem;
		padding: 5px 0;
		text-decoration: none;
		color: var(--bodytext);
		font-weight: 800;
	}

	.main-navigation a:hover,
	.upper-navigation a:hover {
		color: var(--accent);
		text-underline-offset: 0.45em;
	}

	.menu > .current_page_item > a,
	.menu > .current-menu-item > a,
	.menu > .current_page_parent > a,
	.single-research .menu > #menu-item-89 > a {
		text-decoration: underline;
		text-decoration-color: var(--accent);
		text-underline-offset: 0.45em;
	}

	/* desplega submenú */

	.main-navigation ul ul {
		float: left;
		position: absolute;
		top: 26px;
		left: -999em;
		z-index: 99999;
		text-transform: none;
		background-color: #fff;
		text-align: left;
		margin-left: 0;
		padding-left: 0;
		box-shadow: 0 20px 20px rgba(0, 0, 0, 0.4);
	}

	.main-navigation ul ul ul {
		left: -999em;
		top: 0;
	}

	.main-navigation ul li ul a {
		width: 240px;
		padding: 1em;
		color: var(--bodytext);
		border-bottom: 1px solid #d4d4d4;
		font-weight: 600;
		font-size: 0.875rem;
	}

	.main-navigation ul li ul a:hover {
		color: var(--accent);
		text-underline-offset: 0.25em;
	}

	.main-navigation ul ul li {
		font-size: 0.875rem;
		line-height: 1.2;
		display: block;
		padding-left: 0;
		margin-left: 0;
	}

	.main-navigation ul li:hover > ul,
	.main-navigation ul li.focus > ul {
		left: auto;
		margin-left: -1em;
	}

	.main-navigation ul ul li:hover > ul,
	.main-navigation ul ul li.focus > ul {
		left: 100%;
	}

	/* Menú superior preheader */

	.upper-navigation {
		top: 24px;
		right: 40px;
	}

	.upper-navigation li {
		margin: 0 0 0 25px;
		font-size: 0.8125rem;
	}

	.upper-navigation #searchform input {
		font-size: 0.8125rem;
		border: var(--border);
	}

	.upper-navigation #searchform input:focus {
		color: #000;
		border-color: #000;
	}
}

/* VERSIONS GRANS */

@media (min-width: 1100px) {
	#menu-principal {
		gap: 1.4em;
	}

	.main-navigation a {
		display: block;
		font-size: 1rem;
	}
}

@media (min-width: 1250px) {
	#menu-principal {
		gap: 40px;
	}

	.main-navigation #menu-principal > li:first-of-type {
		display: block !important;
	}
}
