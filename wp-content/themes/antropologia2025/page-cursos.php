<?php

/*
Template name: Cursos 
*/


get_header();



?>

		<main id="main" class="agenda site-main wrap" role="main">


		<?php 

			$now = current_time('timestamp'); // Get current timestamp, respecting WordPress timezone
		
			$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
	
		?>

		<?php 
		if($paged==1):
		?>
		<h1 class="section_title"><a href="<?php echo home_url()?>/cursos"><?php _e("Cursos","ppp");?></a> </h1>
		<?php endif?>

		<div id="archive_content">
			<div id="archive_llistat">
		<?php
				
				if($paged==1):
		
		// Query for future courses only
		$args = array(
			'post_type' => 'event',
			'tax_query' => array(
				array(
					'taxonomy' => 'category',
					'field' => 'slug',
					'terms' => 'curs',
					'operator' => 'IN',
				),
			),
			'meta_key' => PIMPAMPUM_PREFIX.'start_date',
			'orderby' => 'meta_value_num', // Use numeric ordering for timestamp
			'order' => 'ASC',
			'meta_query' => array(
				array(
					'key' => PIMPAMPUM_PREFIX.'start_date',
					'value' => $now,
					'compare' => '>=', // Only show events with start date in the future
					'type' => 'NUMERIC' // Use NUMERIC for timestamp comparison
				)
			),
			'paged' => $paged
		);
		
		global $wp_query;
		$wp_query = new WP_Query($args);
		
		if ($wp_query->have_posts()) :
			while (have_posts()) : the_post();
				get_template_part('ppp-templates/events/event', 'teaser');
			endwhile;
			the_posts_pagination(array('mid_size' => 2));
	
		endif;
	endif;
		?>


			<h2 class="section_title"><?php _e("Cursos anteriors","ppp")?></h2>

			<?php

			$paged=( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;

			// Query for future courses only
			$args = array(
			'post_type' => 'event',
			'posts_per_page'=>5,
			'tax_query' => array(
				array(
					'taxonomy' => 'category',
					'field' => 'slug',
					'terms' => 'curs',
					'operator' => 'IN',
				),
			),
			'meta_key' => PIMPAMPUM_PREFIX.'start_date',
			'orderby' => 'meta_value_num', // Use numeric ordering for timestamp
			'order' => 'DESC',
			'meta_query' => array(
			array(
				'key' => PIMPAMPUM_PREFIX.'start_date',
				'value' => $now,
				'compare' => '<', // Only show events with start date in the future
				'type' => 'NUMERIC' // Use NUMERIC for timestamp comparison
				)
			),
			'paged' => $paged
			);

			$the_query = new WP_Query( $args );

			if ( $the_query->have_posts() ) {
			while ( $the_query->have_posts() ) {
				$the_query->the_post();
				get_template_part('ppp-templates/events/event', 'teaser');

			}
			}

			 // Use the specific query for pagination
			 echo paginate_links(array(
				'base' => get_pagenum_link(1) . '%_%',
				'format' => '?paged=%#%',
				'current' => max(1, $paged),
				'total' => $the_query->max_num_pages,
				'prev_text' => __('« Anterior', 'ppp'),
				'next_text' => __('Següent »', 'ppp'),
				'mid_size' => 2
			));


			?>

			<?php the_posts_pagination(array('mid_size' => 2)); ?>
			
			</div>
		</div>
		</main>

<?php get_footer(); ?>
