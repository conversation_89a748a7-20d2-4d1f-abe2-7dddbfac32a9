<?php
get_header();

$date="";
$mes="";
		if(isset($_GET['date'])){
			$date=$_GET['date'];

			$mes=$months[(int)(date("m",strtotime($date)))-1];
			$year=date("Y",strtotime($date));
			$start_date = strtotime(date($date.'-01')); // First day of the month
			$end_date = strtotime(date($date.'-t')); // 't' gets the last day of the month


			//echo date("Y-m-d",date($start_date))." ".date("Y-m-d",date($end_date));

			$meta_query = array(
				'key'       => PIMPAMPUM_PREFIX.'start_date',
				'value'     => array($start_date, $end_date),
				'compare'   => 'BETWEEN',
				//'type'      => 'numeric'
			);


		}


?>

		<main id="main" class="agenda site-main wrap" role="main">
		<h1 class="section_title"><a href="<?php echo home_url()?>/agenda"><?php _e("Agenda","ppp");?></a> <?php
		if($mes!=""){
			echo "/ ".$mes;
			echo " ".$year;
		}
			?></h1>
		<div id="archive_content">
			<div id="archive_llistat">
		<?php
		$paged = ( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;




			//les propers a partir d'avui ordenades de menys a més
			$args=array(
				'post_type'=>'event',
					//'posts_per_page'=>-1,

				'meta_query' => array(
					'AND',
				array(
					'key' => PIMPAMPUM_PREFIX.'start_date',
					'value' => current_time('timestamp')-24*60*60*7,
					'compare' => '>='
				)
				),
				'tax_query' => array(
					array(
						'taxonomy' => 'category',
						'field' => 'slug',
						'terms' => 'curs',
						'operator' => 'NOT IN',
					),
				),
				'meta_key'       => PIMPAMPUM_PREFIX.'start_date',
				'orderby'        => 'meta_value_num',
				'order'          => 'ASC',

			'paged'=>$paged
		);

		if(isset($_GET['ica'])){
			unset($args['meta_query']);
			$args['order']='DESC';
			$args['category_name']='activitat-ica';
		}


		if($date!=""){
			$args['meta_query']=$meta_query;
		}


	global $wp_query;
	$wp_query=new WP_Query($args );

		while ( have_posts() ) : the_post();
		get_template_part( 'ppp-templates/events/event','teaser');
		endwhile;
		the_posts_pagination( array( 'mid_size' => 2 ) );
	   //get_template_part("template-parts/pagination");
		?>
</div>
			<?php get_sidebar("agenda")?>
		</div>
		</main>

<?php get_footer(); ?>
