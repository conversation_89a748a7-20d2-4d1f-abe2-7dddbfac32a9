/*
Theme Name: Antropologia 2025
Theme URI: http://www.pimpampum.net
Author: Pimpampum.net
Author URI: http://www.pimpampum.net
Description: Tema a mida per a Pimpampum
Version: 1.0.0
er
License URI: http://www.gnu.org/licenses/gpl-2.0.html

*/

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
html {
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
}
body {
	margin: 0;
}
main {
	display: block;
}
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}
hr {
	box-sizing: content-box;
	height: 0;
	overflow: visible;
}
pre {
	font-family: monospace, monospace;
	font-size: 1em;
}
a {
	background-color: transparent;
}
abbr[title] {
	border-bottom: none;
	text-decoration: underline;
	text-decoration: underline dotted;
}
b,
strong {
	font-weight: bolder;
}
code,
kbd,
samp {
	font-family: monospace, monospace;
	font-size: 1em;
}
small {
	font-size: 80%;
}
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sub {
	bottom: -0.25em;
}
sup {
	top: -0.5em;
}
img {
	border-style: none;
	display: block;
}
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-size: 100%;
	line-height: 1.15;
	margin: 0;
}
button,
input {
	overflow: visible;
}
button,
select {
	text-transform: none;
}
button,
[type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
	border-style: none;
	padding: 0;
}
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
	outline: 1px dotted ButtonText;
}
fieldset {
	padding: 0.35em 0.75em 0.625em;
}
legend {
	box-sizing: border-box;
	color: inherit;
	display: table;
	max-width: 100%;
	padding: 0;
	white-space: normal;
}
progress {
	vertical-align: baseline;
}
textarea {
	overflow: auto;
}
[type="checkbox"],
[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
	height: auto;
}
[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}
[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}
::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}
details {
	display: block;
}
summary {
	display: list-item;
}
template {
	display: none;
}
[hidden] {
	display: none;
}
.video-wrapper {
	position: relative;
	padding-bottom: 56.25%;
	height: 0;
}
.videowrapper iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
img,
canvas {
	max-width: 100%;
	height: auto;
}
* {
	box-sizing: border-box;
}

:root {
	--mainfont: "Libre Franklin", sans-serif;
	--headerfont: "Bricolage Grotesque", sans-serif;
	--bodytext: #000;
	--gristext: #767676;
	--accent: #b4362e;
	--grisfons: #f2f2f2;
	--bgpage: #fff;
	--border: 1px solid #d4d4d4;
	--radius: 6px;
	--gapXS: 10px;
	--gapS: 20px;
	--gapM: 40px;
	--gapL: 60px;
	--gapXL: 80px;
	--gapXXL: 100px;
	--gapXXXL: 120px;
}

/*-------------------------
Estuctura
--------------------------*/

.wrap,
.wrap_home_inner {
	width: 95%;
	max-width: 1600px;
	margin: 0 auto;
	position: relative;
	padding-left: 20px;
	padding-right: 20px;
}

body,
button,
input,
select,
textarea {
	font-family: var(--mainfont);
	font-optical-sizing: auto;
	font-style: normal;
	font-weight: 400;
	font-size: 1.0625rem;
	line-height: 1.5;
	color: var(--bodytext);
	background-color: var(--bgpage);
}

#main {
	padding-bottom: 60px;
}

/* header */

#masthead {
	position: relative;
	background-color: var(--bgpage);
	margin-bottom: 20px;
}

.header_container {
	background-color: #fff;
	padding: 20px 20px 0 20px;
}

.logo {
	margin: 0;
}

.logo img {
	width: 40px;
}

/* footer */

.logos_footer {
	padding: 40px 0;
}

.info_footer {
	padding: 60px 0 30px 0;
	line-height: 1.2;
	font-size: 0.875rem;
	color: #fff;
	background-color: #000;
}

.info_footer .widget {
	margin-bottom: 40px;
}

.info_footer a,
.info_footer a:visited {
	color: #fff;
	text-decoration: none;
}

.info_footer a:hover {
	text-decoration: underline;
}

.info_footer h2 {
	font-size: 0.875rem;
}

.info_footer p,
.info_footer h2 {
	margin: 0.25em 0;
}

.widget#text-4 p {
	margin-bottom: 20px;
}

a.footer_twitter::before {
	content: url(img/ico_twitter_white.svg);
	margin-right: 10px;
	vertical-align: middle;
}

a.footer_bluesky::before {
	content: url(img/ico_bluesky_white.svg);
	margin-right: 10px;
	vertical-align: middle;
}

a.footer_instagram::before {
	content: url(img/ico_instagram_white.svg);
	margin-right: 10px;
	vertical-align: middle;
}

a.footer_facebook::before {
	content: url(img/ico_facebook_white.svg);
	margin-right: 10px;
	vertical-align: middle;
}

a.footer_youtube::before {
	content: url(img/ico_youtube_white.svg);
	margin-right: 10px;
	vertical-align: middle;
}

.menu-footer-container {
	width: 100%;
	margin-top: 60px;
}

.menu-footer-container ul {
	margin: 0;
	padding: 0;
}

.menu-footer-container li {
	list-style-type: none;
	display: inline;
	margin-right: 1.5em;
}

/* ICA - sidebar */

.sidebar {
	font-size: 1rem;
}

.caixa_sidebar {
	background-color: var(--grisfons);
	padding: var(--gapM);
	margin-bottom: var(--gapL);
}

.sidebar h2,
.arxius h2,
.caixa_sidebar .desc {
	color: var(--accent);
	font-size: 1rem;
	line-height: 1;
	margin-bottom: 10px;
}

.caixa_sidebar h2,
.arxius h2,
.sidebar h2.titol,
.caixa_sidebar h2,
.caixa_sidebar .desc {
	margin-top: 30px;
	margin-bottom: 5px;
	display: block;
}

.caixa_sidebar > h2:first-child,
.caixa_sidebar div:first-child h2,
.arxius h2:first-of-type,
#research_content h2:first-of-type,
.caixa_sidebar div:first-child .desc {
	margin-top: 0;
}

.caixa_sidebar h2 a {
	color: var(--accent);
}
.caixa_sidebar p {
	margin-left: 20px;
}

.sidebar .archive_mini article {
	align-items: flex-start;
}

.sidebar img.featured-image {
	margin-bottom: var(--gapL);
}

.sidebar ul {
	padding: 0;
	margin-left: 20px;
	list-style-type: none;
}

.sidebar li {
	margin-bottom: 0.25em;
	list-style-type: none;
}

.sidebar a {
	text-decoration: none;
}

.sidebar a:hover {
	text-decoration: underline;
}

/*-------------------------
Generals
--------------------------*/

a,
a:visited {
	color: var(--bodytext);
	word-wrap: break-word;
	text-underline-offset: 3px;
}

a:hover,
a:focus,
a:active {
	text-decoration: underline;
	text-decoration-color: var(--accent);
}

a:focus {
	outline: thin dotted;
}

a:hover,
a:active {
	outline: 0;
}

a.tot {
	width: 15px;
	height: 15px;
	display: block;
	text-indent: -999em;
	background: url(img/ico_mes.svg) no-repeat center center;
	position: absolute;
	right: 0;
	top: 0px;
	background-size: contain;
}

a[target="_blank"]:not([download])::after {
	content: " ";
	display: inline-block;
	width: 0.75em;
	height: 0.75em;
	background: url(img/ico_extern.svg) no-repeat center center;
	background-size: contain;
	margin-left: 10px;
	vertical-align: middle;
	opacity: 0.6;
}

a[target="_blank"]:not([download]):hover::after {
	opacity: 1;
}

p,
ul,
ol {
	margin-top: 0;
	margin-bottom: 1em;
}

ul,
ol {
	margin: 0 0 1.5em 0;
	padding-left: 1em;
	list-style: none;
}

strong,
b {
	font-weight: 700;
}

figure {
	margin: 0;
}

figcaption {
	font-size: 0.875rem;
}

h1,
h2,
h3,
h4,
h5,
h6,
.caixa_sidebar .desc {
	font-weight: 700;
	font-family: var(--headerfont);
}

h1 {
	font-size: 1.5rem;
	line-height: 1.16;
	margin-bottom: 30px;
	margin-top: 0;
}

h1.section_title,
h2.section_title,
p.section_title {
	font-family: var(--headerfont);
	color: var(--accent);
	font-size: 1.25rem;
	font-weight: 500;
	line-height: 1;
	padding: 2px 0 18px;
	border-bottom: var(--border);
	margin-bottom: var(--gapS);
	text-decoration: none;
	grid-column: 1/-1;
}

h1.section_title::before,
p.section_title::before,
.caixa_sidebar h2::before,
#archive_llistat h2.section_title::before,
.arxius h2::before,
.sidebar h2.titol::before,
.caixa_sidebar .desc::before {
	content: "";
	height: 1em;
	border-left: 3px solid var(--accent);
	margin-right: 14px;
}

.section_title a {
	color: var(--accent);
	text-decoration: none;
}

.section_title a:hover {
	text-decoration: underline;
}

.home h1.teaser_title {
	font-size: 1.1875rem;
	line-height: 1.16;
	margin: 0.67em 0;
}

.home h1.section_title {
	font-size: 1rem;
	padding-bottom: var(--gapS);
	margin: 0;
	border-bottom: 0;
}

/*h1.teaser_title,
h2.teaser_title {
	font-size: 1.25rem;
	margin-bottom: 0.75em;
	line-height: 1.2;
}*/

h2.title_mes {
	color: var(--bodytext);
	font-size: 1.125rem;
	line-height: 1;
	font-weight: 700;
}

h2.title_mes a,
h1.teaser_title a {
	text-decoration: none;
}

h1.teaser_title a:hover {
	text-decoration: underline;
}

.entry-content,
.fotomoment-intro-text {
	text-align: justify;
	text-justify: inter-word;
	hyphens: auto;
}

.data_ppp,
.pub_item_numero {
	color: var(--accent);
	font-size: 0.8125rem;
	font-weight: 600;
	line-height: 1.15;
	margin-bottom: 0.5em;
}

.veure_mapa {
	font-size: 0.8125rem;
	font-weight: bold;
	margin-top: -10px;
}

.bt a,
.bt a:visited,
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
	background-color: var(--bodytext);
	/* text-transform: uppercase; */
	font-size: 0.875rem;
	padding: 10px 20px;
	text-decoration: none;
	color: #fff;
	border-radius: 0;
	border: none;
}

.clear {
	clear: both;
}

.screen-reader-text {
	display: none;
}

.wpcf7 button,
.wpcf7 input,
.wpcf7 select,
.wpcf7 textarea {
	width: 100%;
}

div.wpcf7-validation-errors,
div.wpcf7-acceptance-missing {
	max-width: 600px;
	background-color: #fff;
	border-radius: 6px;
	border: none;
	padding: 20px;
	margin: 0px auto;
	text-align: center;
}

.flex {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.llegir-mes {
	text-align: center;
}

.llegir-mes span {
	font-weight: 700;
	font-size: 0.875rem;
	background: url(img/arrow_down_black.svg) no-repeat 90% center;
	text-align: center;
	background-color: #fff;
	padding-right: 30px;
	padding-left: 10px;
	position: relative;
	z-index: 2;
}

.llegir-mes:before {
	content: "";
	display: block;
	border-bottom: var(--border);
	position: relative;
	top: 15px;
	z-index: 1;
}

.llegir-mes:hover {
	cursor: pointer;
}

.hide_mobile {
	display: none;
}

hr.wp-block-separator {
	margin: 2em 0;
	border: none;
	height: 1px;
	background-color: #d4d4d4;
	color: #d4d4d4;
	clear: both;
}

/*-------------------------
ICA - HOME
--------------------------*/

/**** NOVA 2025 *****/

.home-text {
	max-width: calc(900px + 10vw);
	text-align: center;
	font-family: var(--headerfont);
	font-weight: 500;
	font-size: clamp(18px, 2.8vw, 36px);
	line-height: 1.1;
	text-wrap: balance;
	margin: var(--gapM) auto;
	padding: 0 5vw;
}

/* HOME - Slideshow */

body.home .home-slideshow-section {
	position: relative;
	width: 100%;
}

body.home .slides-container {
	position: relative;
	overflow: hidden;
	background-color: var(--accent);
	min-height: 380px !important;
}

body.home .slide {
	display: none;
	flex-wrap: wrap;
	align-items: center;
	padding: 0;
	transition: opacity 0.5s ease;
	opacity: 0;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	visibility: hidden; /* Hide completely */
	flex-direction: column-reverse;
}

body.home .slide.active {
	display: flex !important;
	opacity: 1;
	position: relative;
	z-index: 5;
	visibility: visible !important;
}

body.home .slide-content,
body.home .slide-image-wrapper {
	width: 100%;
	height: 100%;
}

/* Slideshow - Contingut */

body.home .slide-content {
	display: flex;
	flex-direction: column;
	justify-content: end;
	padding: var(--gapM) 5vw var(--gapS) 5vw;
	color: #fff;
}

body.home .slide-content h2 {
	color: #fff;
	font-size: clamp(1.5rem, 2.5vw, 2.5rem);
	line-height: 1.1;
	margin-top: 0;
	margin-bottom: 0.5em;
	font-weight: 800;
}

body.home .slide-text {
	font-size: 1rem;
}

/* Slideshow - Imatge */

body.home .slide-image {
	width: 100%;
	position: relative;
}

body.home .slide-image {
}

body.home .slide-image img {
	aspect-ratio: 2.5;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* Slideshow - Fletxes */

body.home .slide-nav {
	display: flex;
	gap: 10px;
	margin-top: var(--gapS);
}

body.home .slide-bullet {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background-color: rgba(255, 255, 255, 0.5);
	border: none;
	padding: 0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

body.home .slide-bullet.active {
	background-color: #fff;
}

/* ICA - HOME */

section[id*="home_"] .wrap,
.wrap_home_inner {
	padding-top: var(--gapM);
	padding-bottom: var(--gapM);
	position: relative;
}

.home_section_header {
	position: relative;
	grid-column: 1/-1;
}

#home_destacats,
#home_publicacions,
#home_noticies,
#home_monografics {
	background-color: var(--grisfons);
}

#home_destacats .teaser {
	margin-bottom: 30px;
}

#home_agenda .teaser {
	margin-bottom: var(--gapS);
}

.home_fotomoment_header p,
.home_monografics_excerpt {
	font-size: 0.875rem;
	margin-bottom: 0;
	line-height: 1.35;
}

.home_monografics_thumbnail {
	margin: var(--gapXS) 0;
}

#home_fotomoment {
	transform: rotate(-1deg);
}

.home_fotomoment_header {
	margin-bottom: var(--gapXS);
	position: relative;
}

.home_fotomoment_header h1.section_title {
	border-bottom: none;
	margin-bottom: 0;
	padding-bottom: 0;
}

.home_fotomoment_header p {
	margin-bottom: 0.5em;
}

.home_fotomoment_media img {
	max-height: 500px;
	width: auto;
	margin: 0 auto;
	box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
	border-radius: 4px;
}

#home_agenda .wrap {
	display: grid;
	grid-template-columns: 1fr;
	column-gap: 30px;
}

#home_agenda .teaser {
	background-color: var(--grisfons);
}

div .teaser:last-of-type {
	margin-bottom: 0 !important;
}

#home_agenda .teaser img {
	margin-bottom: 0;
}

#home_agenda .teaser_agenda_info {
	position: relative;
	padding: var(--gapS) var(--gapS) var(--gapM) var(--gapS);
}

p.activitat_ica {
	margin: 0 5px 5px 0;
	padding: 3px 5px;
	color: #fff;
	font-size: 0.625rem;
	text-transform: uppercase;
	font-weight: 600;
	background-color: var(--accent);
	line-height: 1;
	font-family: var(--headerfont);
}

#home_agenda p.activitat_ica {
	position: absolute;
	top: 0;
}

#home_publicacions {
	background-color: var(--grisfons);
}

#home_publicacions .bloc_destacats,
#post-181 ul {
	display: grid;
	grid-template-columns: 1fr;
	gap: var(--gapS);
}

#post-181 ul {
	margin: 0 0 var(--gapM) 0;
	padding: 0;
}

#post-181 .entry-content li::before {
	list-style-type: none;
	content: none;
}

#home_publicacions .pub-teaser,
#post-181 li {
	padding: var(--gapS);
	display: flex;
	flex-direction: column;
	position: relative;
	font-size: 1rem;
	line-height: 1.25rem;
	background-color: #fff;
}

#post-181 li {
	background-color: var(--grisfons);
	margin: 0;
}

#home_publicacions p,
#post-181 li {
	font-family: var(--headerfont);
	font-size: 1.25rem;
	font-weight: 600;
	line-height: 1;
	margin-top: 0;
	margin-bottom: 0.5em;
}

#home_publicacions a,
#home_publicacions a:visited,
#post-181 li a,
#post-181 li a:visited {
	text-decoration: none;
	display: block;
}

#home_publicacions a:hover,
#post-181 li a:hover {
	text-decoration: underline;
}

#home_publicacions a::before,
#post-181 li a::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
}

/* ICA - teasers en general */

.teaser {
	font-size: 0.875rem;
	line-height: 1.35;
}

.teaser_mini {
	margin-bottom: var(--gapS);
}

.teaser img {
	width: 100%;
	aspect-ratio: 3/2;
	object-fit: cover;
	margin-bottom: var(--gapXS);
}

#home_destacats .teaser img,
#home_agenda .teaser img {
	aspect-ratio: 2;
}

.teaser h2,
h1.teaser_title {
	font-size: 1.125rem;
	line-height: 1.2;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.25em;
}

.sidebar .teaser h2 {
	font-size: 1rem;
	line-height: 1.1;
}

.teaser-title a,
h2 a {
	text-decoration: none;
}

.teaser-title a:hover,
h2 a:hover {
	color: var(--bodytext);
}

.teaser div[class*="info"] p:last-of-type {
	margin-bottom: 0;
}

.teaser div[class*="info"] p.data_ppp {
	font-size: 0.75rem;
	font-weight: 600;
	margin-bottom: 0.5em;
}

/* teaser recerca */

.research-teaser {
	padding: var(--gapS) 0;
	border-bottom: var(--border);
}

.research-teaser:first-of-type {
	padding-top: 0;
}

.research_teaser_title {
	margin-bottom: 0;
	font-size: 0.75rem;
	font-weight: 500;
}

.recerca_autors p {
	font-size: 0.9375rem;
	margin-bottom: 0;
}

/* Formularis Associa't i activitats */

.page-id-2729 .wpcf7 {
	max-width: 400px;
	line-height: 1.3;
}

.page-id-2729 input,
.formulari-activitat input,
.page-id-2729 textarea,
.formulari-activitat textarea,
.page-id-2729 select,
.formulari-activitat select {
	border: 1px solid #000;
	margin: 5px 0 1em 0;
	padding: 5px 8px;
}

.wpcf7 input[type="checkbox"],
.formulari-activitat input[type="checkbox"] {
	width: auto;
}

.wpcf7 input[type="submit"]:disabled {
	background-color: #999;
}

.wpcf7 input[type="submit"] {
	background-color: #333;
	transition: background-color 0.2s;
}

.wpcf7 input[type="submit"]:not(:disabled):hover {
	background-color: #000;
	transition: background-color 0.2s;
	cursor: pointer;
}

.wpcf7-acceptance {
	font-size: 80%;
}

.wpcf7-acceptance .wpcf7-list-item {
	margin-left: 0;
}

.dades {
	line-height: 1.1;
}

.formulari-activitat {
	border: var(--border);
	padding: 14px 30px;
	margin-bottom: 2em;
}

.formulari-activitat > h2.activitat-title {
	margin: 0;
	font-size: 1.5rem;
	background: url(img/arrow_down_black.svg) no-repeat 98% center;
}

.formulari-activitat > h2.activitat-title:hover {
	cursor: pointer;
}

.activitat-form {
	margin-top: 20px;
}

body:not(.logged-in) .formulari-activitat textarea[name="activitat-title"],
body:not(.logged-in) .formulari-activitat input[name="activitat-data"] {
	padding: 0;
	border: none;
	height: auto;
	appearance: none;
	resize: none;
}

body:not(.logged-in) .formulari-activitat textarea[name="activitat-title"]:focus,
body:not(.logged-in) .formulari-activitat textarea[name="activitat-data"]:focus {
	outline: none;
}

.formulari-activitat input[type="submit"] {
	padding: 12px;
	font-size: 1.125rem;
}

/*-------------------------
INTERIORS
--------------------------*/

/* ICA - Pàgines genèriques */

#page_intro,
#page_submenu {
	margin-bottom: 40px;
}

#excerpt {
	font-weight: 700;
	font-size: 1rem;
	line-height: 1.25;
	margin-bottom: 40px;
}

#contingut li,
.entry-content li {
	margin-bottom: 0.5em;
}

#contingut li::before,
.entry-content li::before {
	content: "\2022";
	color: var(--accent);
	font-weight: bold;
	padding-right: 10px;
	margin-left: -15px;
}

.imatge_destacada {
	margin: 2em 0;
}

.arxius {
	background-color: var(--grisfons);
	padding: 30px 40px;
	margin: 40px 0;
	clear: both;
}

ul.files {
	margin: 0;
	padding: 0;
	list-style-type: none;
	font-size: 1rem;
}

ul.files li {
	margin-bottom: 0.5em;
}

ul.files li::before {
	content: none;
	padding-right: 0;
	margin-left: 0;
}

ul.files a:hover {
	text-decoration: underline;
}

/* ICA - Sidebar menu */

#page_submenu_sidebar {
	margin-bottom: 40px;
}

#page_submenu #sidebar_menu {
	margin: 0;
	padding-left: 20px;
	border-left: 3px solid #000;
}

#sidebar_menu li {
	list-style-type: none;
	/* text-transform: uppercase; */
	line-height: 1.11;
	margin-bottom: 20px;
	font-weight: 400;
}

#sidebar_menu li:last-child {
	margin-bottom: 0;
}

#sidebar_menu li a {
	color: var(--gristext);
	text-decoration: none;
}

#sidebar_menu li a:hover {
	color: var(--accent);
}

#sidebar_menu li.selected a {
	color: #000;
	font-weight: 700;
}

.submenu-header ul {
	display: flex;
	flex-wrap: wrap;
	padding-left: 0;
	margin-left: 0;
}

.submenu-header #sidebar_menu li {
	margin-bottom: 10px;
}

.submenu-header a {
	border-left: 3px solid var(--gristext);
	padding-left: 20px;
	padding-right: 20px;
	font-size: 1rem;
}

.submenu-header li.selected a {
	border-color: #000;
}

.submenu-header #sidebar_menu li a:hover {
	color: var(--bodytext);
	border-color: #000;
}

/* ICA - Llistat grups recerca */

ul#llistat_grups {
	margin: 0;
	padding: 0;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	grid-gap: 20px;
}

.research-group-teaser {
	list-style-type: none;
	border: 1px solid #eaeaea;
	border-bottom: var(--border);
	background-color: var(--grisfons);
}

.research-group-teaser h2 {
	font-size: 1.125rem;
	line-height: 1.2;
	margin: 0;
	padding: var(--gapS) 30px;
}

.research-group-teaser figure {
	background-color: #fff;
	width: 100%;
	height: 150px;
	margin: 0 auto;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: var(--gapS);
}

.research-group-teaser figure a {
	display: block;
}

.research-group-teaser img {
	object-fit: contain;
	max-width: 80%;
	max-height: 110px;
	margin: 0 auto;
}

.research-group-teaser a {
	text-decoration: none;
}

.research-group-teaser a:hover {
	color: var(--bodytext);
	text-decoration: underline;
	text-decoration-color: var(--accent);
}

.activitat_recerca {
	margin-bottom: 1.5em;
}

.activitat_recerca h3 {
	font-size: 1rem;
	line-height: 1.125;
	margin: 0;
}

/* ICA - Single Grup de recerca */

#research_content #contingut h2 {
	color: var(--accent);
	font-size: 1.25rem;
	line-height: 1.1;
}

#research_logo {
	border: var(--border);
	padding: 40px 36px;
	margin-bottom: 40px;
	text-align: center;
}

#research_content .opcio {
	border-bottom: var(--border);
}

#research_content .presentacio h2 {
	margin-bottom: 30px;
}

#research_content .opcio > h2 {
	font-size: 1.25rem;
	line-height: 1;
	padding: 20px 0;
	margin: 0;
	background: url(img/arrow_down.svg) no-repeat 95% center;
}

#research_content .opcio > h2.closed:hover {
	cursor: pointer;
	color: #000;
}

#research_content .opcio > h2.opened {
	background: url(img/arrow_up.svg) no-repeat 95% center;
}

/* ICA -  Archive - Notícies i agenda llistat teasers */

#archive_llistat .teaser_archive_info .flex {
	align-items: center;
}

#archive_llistat .teaser_archive {
	display: flex;
	padding-bottom: var(--gapS);
	margin-bottom: var(--gapS);
	border-bottom: var(--border);
}

#archive_llistat .archive_thumbnail {
	width: 120px;
	margin-right: 25px;
}

#archive_llistat .archive_thumbnail img {
	width: 100%;
	height: 100%;
	max-height: 220px;
	object-fit: contain;
	object-position: center top;
}

#archive_llistat .teaser_archive_info {
	flex: 1;
	font-size: 0.9375rem;
	line-height: 1.3;
}

#archive_llistat h2.section_title {
	margin-top: var(--gapL);
}

/* ICA - Single notícia i agenda */

.entry-content {
	display: flow-root;
}

.single_thumbnail {
	margin: 0 36px 2em 0;
}

.single .wp-block-image img {
	max-height: 70vh;
	width: auto;
}

.wp-block-file {
	margin: var(--gapM) 0;
}

/* ICA - Publicacions en general */

#llistat_publicacions {
	margin: 0;
	padding: 0;
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	grid-gap: 36px;
}

.page-template-page-publicacions p.section_title {
	margin-bottom: 18px;
}

.pub_item_teaser {
	background-color: var(--grisfons);
	padding: 40px 30px 40px 36px;
	display: flex;
}

.pub_item_teaser figure {
	margin-right: 36px;
	width: 120px;
	max-height: 200px;
	background-color: var(--grisfons);
}

.pub_item_teaser img {
	aspect-ratio: initial;
	width: 100%;
	height: 100%;
	object-fit: cover;
	margin: 0;
}

.pub_item_info {
	flex: 1;
}

/* quaderns -e */

#llistat_publicacions.quaderns-e {
	grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
	grid-auto-rows: 1fr;
}

.pub_item_teaser.pub-type-quaderns-e {
	padding: 40px 30px 20px 36px;
	flex-direction: column;
	justify-content: space-between;
}

.pub-type-quaderns-e h2 {
	font-size: 1.5rem;
	margin-top: 0;
	margin-bottom: 40px;
}

.pub-type-quaderns-e p {
	margin-bottom: 0;
}

.pub-type-quaderns-e a {
	text-decoration: none;
}

/* ICA - Foto del moment - CSS del overlay a la plantilla */

.fotomoment-archive h1.section_title {
	margin-bottom: 0;
	border-bottom: none;
}

.fotos-grid {
	margin-top: var(--gapM);
	margin-bottom: var(--gapM);
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
	grid-gap: var(--gapS);
}

.fotomoment-intro {
	background-color: var(--grisfons);
	padding: var(--gapL) 0;
	margin-bottom: var(--gapL);
}

.fotomoment-intro-media {
	transform: rotate(-0.8deg);
	font-size: 0.875rem;
}
.fotomoment-intro-media h3 {
	font-size: 0.875rem;
}

.fotomoment-intro-text {
	margin-top: var(--gapS);
	padding-top: var(--gapS);
	border-top: var(--border);
}

.fotomoment-intro-media img.fotomoment-ultima {
	border-radius: var(--radius);
	box-shadow: 4px 4px 18px rgba(0, 0, 0, 0.1);
}

.fotomoment-intro p.has-small-font-size {
	line-height: 1.2;
}

.fotomoment-intro h3 {
	margin-bottom: 0;
	margin-top: 0.5em;
}

.fotomoment-autor,
.fotomoment-data {
	margin-bottom: 0;
}

.latest-foto {
	cursor: pointer;
	transition: opacity 0.3s ease;
	position: relative;
}

.latest-foto:after,
.foto-item:after {
	content: url(img/ico_foto_info.svg);
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: white;
	width: 120px;
	height: 120px;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.foto-item:after {
	content: url(img/ico_foto_info_mini2.svg);
	width: 60px;
	height: 60px;
	object-fit: contain;
}

.latest-foto:hover:after,
.foto-item:hover:after {
	opacity: 1;
}

/* Monogràfics - ARCHIVE */

.monografics_grid,
.monografic-documents-grid,
.monografic-publicacions-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: var(--gapM);
}

.monografics_grid > article {
	background-color: var(--grisfons);
}

.monografics_grid > article .archive_thumbnail {
	margin: 0;
}

.monografics_grid > article .archive_thumbnail img {
	aspect-ratio: 1.75;
	object-fit: cover;
}

.monografics_grid > article h2 {
	margin-top: 0;
}

.monografics_grid .teaser_archive_info {
	padding: var(--gapM);
}

/* Monogràfics - SINGLE */

.monografic-sections .section-container {
	padding: var(--gapL) 0;
}

.monografic-sections .section-container:nth-of-type(2n + 1) {
	background-color: var(--grisfons);
}
.monografic-sections h2.section-title {
	color: var(--accent);
	border-bottom: var(--border);
	padding-bottom: var(--gapXS);
	margin: 0;
}

.monografic-sections article.teaser .teaser_thumbnail {
	margin: 0;
	width: 150px;
	max-height: 200px;
	background-color: var(--grisfons);
	margin-right: var(--gapS);
}

.monografic-sections article.teaser .teaser_thumbnail img {
	aspect-ratio: initial;
	width: 100%;
	height: 100%;
	object-fit: cover;
	margin: 0;
}

p.publicacio_meta {
	margin-bottom: 0;
}

.teaser_publicacio .data,
.teaser_publicacio .autor {
	font-weight: 500;
}

/* tipus d'arxiu i generals */

.files a[href$=".pdf"],
.entry-content a[href$=".pdf"] {
	background: url(img/pdf.png) no-repeat left top;
}

.files a[href$=".doc"],
.files a[href$=".docx"],
.entry-content a[href$=".doc"],
.entry-content a[href$=".docx"] {
	background: url(img/doc.png) no-repeat left top;
}

.files a[href$=".xls"],
.entry-content a[href$=".xls"] {
	background: url(img/xls.png) no-repeat left top;
}

.files a[href$=".ppt"],
.entry-content a[href$=".ppt"] {
	background: url(img/ppt.png) no-repeat left top;
}

.files a[href$=".zip"],
.entry-content a[href$=".zip"] {
	background: url(img/zip.png) no-repeat left top;
}

.files a,
.entry-content a[href$=".pdf"],
.entry-content a[href$=".doc"],
.entry-content a[href$=".docx"],
.entry-content a[href$=".xls"],
.entry-content a[href$=".ppt"],
.entry-content a[href$=".zip"],
.files a[href$=".pdf"],
.files a[href$=".doc"],
.files a[href$=".docx"],
.files a[href$=".xls"],
.files a[href$=".ppt"],
.files a[href$=".zip"] {
	text-decoration: none;
	padding-left: 30px;
	min-height: 30px;
	line-height: 1.3;
	background-size: 16px auto;
}

.entry-content a.wp-block-file__button {
	background-image: none;
	padding-left: 1em;
	background-color: black;
}

/* paginacio */

.pagination {
	width: 100%;
	text-align: center;
	font-size: 0.9375rem;
	margin-bottom: 40px;
}

.pagination .page-numbers {
	text-align: center;
	text-decoration: none;
	padding: 12px 14px 10px 14px;
	line-height: 1;
	display: inline-block;
}

.pagination .page-numbers.current {
	font-weight: 700;
	color: var(--bodytext);
	background-color: var(--grisfons);
}

.pagination .page-numbers:hover {
	color: var(--bodytext);
	background-color: var(--grisfons);
}

.pagination {
	margin-top: 40px;
}

/* Resultats cerca */

.hentry.search {
	border-bottom: var(--border);
	padding: 20px 0;
}

.hentry.search p {
	margin-bottom: 0;
}

.hentry.search h3 {
	font-size: 1.25rem;
	margin: 0;
}

.hentry.search a {
	text-decoration: none;
}

.hentry.search a:hover {
	text-decoration: underline;
	text-decoration-color: var(--accent);
}

/* Captions */

.wp-caption {
	margin-bottom: 1.5em;
	max-width: 100%;
}

.wp-caption img[class*="wp-image-"] {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.wp-caption .wp-caption-text {
}

.wp-caption-text {
	font-size: 0.875rem;
	color: #666;
	text-align: left;
	padding: 8px 0 5px 0;
}

/*-------------------------
RESPONSIVE
--------------------------*/

/* ANCHOR 600 */

@media screen and (min-width: 600px) {
	.wrap {
		padding-left: 40px;
		padding-right: 40px;
	}

	.logo img {
		width: 80px;
	}

	#cercador {
		top: 60px;
	}

	.upper-navigation #searchform input {
		width: 110px;
	}

	.upper-navigation #searchform input:focus {
		width: 200px;
		position: static;
	}

	/* ICA - HOME */

	#home_destacats .wrap {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		column-gap: var(--gapS);
	}

	.teaser {
		margin-bottom: 0;
	}

	.teaser_mini {
		display: grid;
		grid-template-columns: 223px 1fr;
		column-gap: 25px;
		padding: var(--gapXS) 0;
		border-bottom: var(--border);
		margin-bottom: 0;
	}

	.sidebar .teaser_mini {
		grid-template-columns: 150px 1fr;
	}

	.home_fotomoment_header {
		display: flex;
		gap: var(--gapS);
		align-items: baseline;
	}

	/* ICA -  Archive - Notícies i agenda llistat teasers */

	#archive_llistat .archive_thumbnail {
		width: 150px;
		background-color: var(--grisfons);
	}

	/* publicacions distribuïdora */
	#post-181 ul {
		grid-template-columns: 1fr 1fr;
	}

	/* ICA - Publicacions en general */

	#llistat_publicacions {
		grid-template-columns: repeat(auto-fill, minmax(550px, 1fr));
	}

	#llistat_publicacions.quaderns-e {
		grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
		grid-auto-rows: 1fr;
	}
}

/* ANCHOR 768 */

@media screen and (min-width: 768px) {
	#main {
		padding-bottom: 100px;
	}

	.wrap {
		padding-left: 40px;
		padding-right: 40px;
	}

	.hide_mobile,
	.submenu-header {
		display: block;
	}

	.hide_gran {
		display: none;
	}

	/* header */

	.home #masthead {
		margin-bottom: 0;
	}

	.header_container {
		padding-top: 30px;
		padding-bottom: 20px;
		background-color: #fff;
	}

	/* ICA - Generals */

	h1 {
		font-size: 2.25rem;
		margin-bottom: 40px;
		margin-top: 0;
	}

	h1.section_title,
	p.section_title {
		margin-bottom: 30px;
		padding-bottom: 18px;
	}

	.fotomoment-archive h1.section_title {
		margin-bottom: 0;
	}

	a.tot {
		width: 21px;
		height: 21px;
		background: url(img/ico_mes.svg) no-repeat center center;
		top: 6px;
	}

	/* ICA HOME + publicacions */

	#home_publicacions .pub-teaser a,
	#post-181 li a {
		padding: 40px 36px;
	}

	#home_publicacions p,
	#post-181 li {
		font-size: 1.5rem;
	}

	/* ICA - home agenda */

	/* ICA - Pàgines genèriques */

	#page_intro,
	#page_submenu {
		margin-bottom: 60px;
	}

	#excerpt {
		font-size: 1.25rem;
	}

	.arxius {
		padding: 30px 30px 60px 40px;
	}

	/* ICA -  Archive - Notícies i agenda llistat teasers */

	#archive_llistat .archive_thumbnail {
		width: 300px;
		margin-right: 40px;
		text-align: center;
	}

	/* footer */

	.logos_footer {
		margin: 0;
	}

	.info_footer .wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: baseline;
		flex-wrap: wrap;
		gap: 40px;
	}

	.info_footer .widget {
		max-width: 400px;
	}

	/* Monogràfics - ARCHIVE */

	.monografics_grid,
	.monografic-documents-grid,
	.monografic-publicacions-grid {
		grid-template-columns: repeat(auto-fill, minmax(480px, 1fr));
	}
}

/* ANCHOR 1100 */

@media screen and (min-width: 1100px) {
	/* generals */

	ul,
	ol {
		margin: 0 0 1.5em 1.5em;
	}

	.home h1.section_title {
		font-size: 1.25rem;
	}

	/* HOME - Slideshow */

	body.home .slides-container {
		min-height: none;
		height: 400px;
	}

	body.home .slide.active {
		display: grid !important;
		grid-template-columns: minmax(2.5vw, 1fr) minmax(300px, 680px) minmax(300px, 920px) minmax(2.55vw, 1fr);
	}

	body.home .slide-content {
		grid-column: 2/3;
		padding: 40px;
		height: 400px;
	}

	body.home .slide-content h2 {
		color: #fff;
	}

	body.home .slide-text {
		font-size: 1.125rem;
	}

	body.home .slide-image {
		grid-column: 3/5;
		height: 400px;
	}

	body.home .slide-image img {
		aspect-ratio: initial;
	}

	body.home .slide-nav {
		margin-top: var(--gapM);
	}

	/* HOME */

	.wrap_home {
		width: 95%;
		max-width: 1600px;
		margin: 0 auto;
		position: relative;
		padding-left: 80px;
		padding-right: 80px;
	}
	.wrap_home_inner {
		width: 100%;
		padding: 0;
	}

	#home_destacats .teaser,
	#home_agenda .teaser {
		margin-bottom: 0;
	}

	#home_agenda .wrap {
		grid-template-columns: repeat(3, 1fr);
	}

	section[id*="home_"] .wrap,
	.wrap_home_inner {
		padding-top: var(--gapL);
		padding-bottom: var(--gapL);
	}

	#home_noticies,
	#home_monografics {
		background-color: transparent;
	}

	#home_actualitat {
		background-color: var(--grisfons);
	}

	#home_publicacions .bloc_destacats {
		grid-template-columns: 2fr 1fr 1fr;
		grid-template-rows: 1fr;
	}

	/* header */

	.header_container {
		padding-top: 40px;
		padding-bottom: 20px;
		background-color: #fff;
	}

	.header_options {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-end;
	}

	.main-navigation {
		margin-top: 0;
	}

	.header_container {
		display: grid;
		grid-template-columns: 80px 1fr;
		justify-content: space-between;
		gap: 40px;
	}

	.upper-navigation {
		position: static;
	}

	.upper-navigation li {
		margin: 0 0 0 55px;
	}

	.upper-navigation li.ico_instagram,
	.upper-navigation li.ico_facebook,
	.upper-navigation li.ico_youtube {
		margin-left: 25px;
	}

	.upper-navigation #searchform input {
		width: 200px;
	}

	/* ICA - destacats home */

	#home_destacats .wrap,
	#home_publicacions {
		grid-template-columns: repeat(4, 1fr);
	}

	#home_publicacions .bloc_destacats {
		grid-template-columns: 1fr 1fr 1fr;
		grid-template-rows: 1fr;
	}

	/* ICA - Actualitat home */

	#home_actualitat .wrap_home,
	#home_monografics_cursos .wrap_home {
		display: grid;
		grid-template-columns: 1fr 1fr;
		column-gap: var(--gapL);
	}

	/* ICA - Pàgines genèriques */

	#page_intro,
	#page_submenu {
		margin-bottom: 60px;
	}

	#page_intro,
	#page_submenu {
		display: grid;
		grid-template-areas: "excerpt . content .";
		grid-template-columns: minmax(300px, 450px) auto minmax(400px, 700px) auto;
		grid-template-rows: auto;
		grid-column-gap: 72px;
		align-items: start;
	}

	#page_intro #excerpt {
		grid-area: excerpt;
	}

	#page_content {
		grid-area: content;
	}

	/* ICA - Pàgines genèriques amb submenú */

	#page_submenu {
		grid-template-areas:
			"titol titol titol titol"
			"sidebar . content .";
		grid-template-columns: minmax(300px, 450px) auto minmax(500px, 700px) auto;
		grid-column-gap: 36px;
	}

	#page_submenu .entry-header {
		grid-area: titol;
	}

	#page_submenu_sidebar {
		grid-area: sidebar;
		margin-bottom: 0;
	}

	#page_submenu article {
		grid-area: content;
	}

	/* ICA - Single Grup de recerca */

	#research_content,
	#single_content {
		display: grid;
		grid-template-areas: "contingut sidebar";
		grid-template-columns: minmax(500px, 700px) minmax(300px, 450px);
		justify-content: space-between;
		grid-template-rows: auto;
		grid-gap: 72px;
		margin-bottom: 80px;
	}

	#contingut,
	.single_info {
		grid-area: contingut;
	}

	#research_sidebar,
	#single_content .sidebar {
		grid-area: sidebar;
	}

	/* ICA -  Notícies i agenda single */

	#archive_content {
		display: grid;
		grid-template-areas: "contingut sidebar";
		grid-template-columns: minmax(500px, 870px) minmax(300px, 430px);
		justify-content: space-between;
		align-items: start;
		grid-template-rows: auto;
		grid-gap: 72px;
	}

	#archive_llistat {
		grid-area: contingut;
	}

	#archive_sidebar {
		grid-area: sidebar;
	}

	/* ICA - Foto del moment */

	.fotomoment-intro .wrap {
		display: grid;
		grid-template-columns: 1.5fr 1fr;
		column-gap: var(--gapL);
	}

	.fotomoment-intro h1 {
		grid-column: 1 / -1;
		margin-bottom: 1em;
	}

	.fotomoment-intro-text {
		margin-top: 0;
		padding-top: 0;
		border-top: none;
	}

	.fotomoment-intro-media,
	.fotomoment-intro-media h3 {
		font-size: 1rem;
	}
}

/* ANCHOR 1250 */

@media screen and (min-width: 1250px) {
	/* header */

	.logo a {
		display: block;
	}

	#header_nav {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-end;
	}
}

/* ANCHOR 1400 */

@media screen and (min-width: 1500px) {
	.wrap {
		width: 85%;
		padding-left: 80px;
		padding-right: 80px;
	}

	.info_footer .widget {
		max-width: none;
	}

	/* Home - slideshow */

	body.home .slides-container {
		height: 500px;
	}

	body.home .slide.active {
		grid-template-columns: minmax(5vw, 1fr) minmax(300px, 680px) minmax(300px, 920px) minmax(5vw, 1fr);
	}

	body.home .slide-content {
		padding-left: 80px;
		height: 500px;
	}

	body.home .slide-image {
		aspect-ratio: 2.5;
		height: 500px;
	}

	#home_destacats .wrap {
		column-gap: var(--gapM);
	}
}
