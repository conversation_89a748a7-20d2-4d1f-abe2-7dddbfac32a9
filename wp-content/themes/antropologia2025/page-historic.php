<?php

/*
Template name: Històric agenda, cursos, notícies 
*/


get_header();



// Get the current URL path
$current_path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');

// Split the path into segments
$segments = explode('/', $current_path);

// Get the first meaningful segment (after any potential WordPress installation directory)
$site_path = parse_url(home_url(), PHP_URL_PATH);
$site_segments = array_filter(explode('/', trim($site_path, '/')));
$start_index = count($site_segments);

// Determine the content type segment
$content_type = isset($segments[$start_index]) ? $segments[$start_index] : '';

// For debugging: remove this in production
// echo "Content type: " . $content_type;

// Set page title and query parameters based on content type
switch($content_type) {
    case 'cursos':
        $page_title = __("Cursos", "ppp");
        $post_type = 'event';
        $category = 'curs';
        $archive_link = home_url('/cursos');
        break;
    case 'agenda':
        $page_title = __("Agenda", "ppp");
        $post_type = 'event';
        $category = '';
        $archive_link = home_url('/agenda');
        break;
    case 'noticies':
        $page_title = __("Notícies", "ppp");
        $post_type = 'post';
        $category = '';
        $archive_link = home_url('/noticies');
        break;
    default:
        $page_title = __("Històric", "ppp");
        $post_type = 'post';
        $category = '';
        $archive_link = home_url();
}
?>



		<main id="main" class="agenda site-main wrap" role="main">


		<?php

			$now = current_time('timestamp'); // Get current timestamp, respecting WordPress timezone
			$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

			// Handle date filtering from URL parameter
			$date_filter = isset($_GET['date']) ? $_GET['date'] : '';
			$start_date = null;
			$end_date = null;

			if (!empty($date_filter)) {
				// Parse the date (format: YYYY-MM)
				$date_parts = explode('-', $date_filter);
				if (count($date_parts) == 2) {
					$year = intval($date_parts[0]);
					$month = intval($date_parts[1]);

					// Create start and end timestamps for the month
					$start_date = mktime(0, 0, 0, $month, 1, $year);
					$end_date = mktime(23, 59, 59, $month, date('t', $start_date), $year);
				}
			}

		?>

		<?php
		if($paged==1):
		?>
		<h1 class="section_title">
			<a href="<?php echo $archive_link ?>"><?php echo $page_title?></a>
			<?php
			// Show the filtered month in the title if date filter is applied
			if (!empty($date_filter)) {
				$catalan_months = array(
					'01' => 'Gener', '02' => 'Febrer', '03' => 'Març', '04' => 'Abril',
					'05' => 'Maig', '06' => 'Juny', '07' => 'Juliol', '08' => 'Agost',
					'09' => 'Setembre', '10' => 'Octubre', '11' => 'Novembre', '12' => 'Desembre'
				);
				$date_parts = explode('-', $date_filter);
				if (count($date_parts) == 2) {
					$year = $date_parts[0];
					$month_num = $date_parts[1];
					$month_name = isset($catalan_months[$month_num]) ? $catalan_months[$month_num] : $month_num;
					echo " / {$month_name} {$year}";
				}
			}
			?>
		</h1>
		<?php endif?>

		<div id="archive_content">
			<div id="archive_llistat">
		<?php

				// For noticies, show main section on all pages; for events, only on page 1
				if($paged==1 || $content_type == 'noticies'):

		// Query for items
		$args = array(
			'post_type' => $post_type,
			'posts_per_page' => get_option('posts_per_page'), // Use WordPress setting
			'paged' => $paged
		);

		// Add category filter for cursos
		if ($content_type == 'cursos') {
			$args['tax_query'] = array(
				array(
					'taxonomy' => 'category',
					'field' => 'slug',
					'terms' => 'curs',
					'operator' => 'IN',
				),
			);
		}

		// Use ppp_start_date for events (agenda and cursos), publication date for posts (noticies)
		if ($post_type == 'event') {
			$args['meta_key'] = PIMPAMPUM_PREFIX.'start_date';
			$args['orderby'] = 'meta_value_num';
			$args['order'] = 'ASC';

			// If date filter is provided, filter by that month, otherwise show future events
			if (!empty($date_filter) && $start_date && $end_date) {
				$args['meta_query'] = array(
					array(
						'key' => PIMPAMPUM_PREFIX.'start_date',
						'value' => array($start_date, $end_date),
						'compare' => 'BETWEEN',
						'type' => 'NUMERIC'
					)
				);
			} else {
				$args['meta_query'] = array(
					array(
						'key' => PIMPAMPUM_PREFIX.'start_date',
						'value' => $now,
						'compare' => '>=', // Only show events with start date in the future
						'type' => 'NUMERIC'
					)
				);
			}
		} else {
			// For posts (noticies), show all posts (they are always in the past)
			$args['orderby'] = 'date';
			$args['order'] = 'DESC';

			// If date filter is provided, filter by that month
			if (!empty($date_filter) && $start_date && $end_date) {
				$args['date_query'] = array(
					array(
						'after' => date('Y-m-d', $start_date),
						'before' => date('Y-m-d', $end_date),
						'inclusive' => true,
					)
				);
			}
			// No else clause - show all posts when no date filter
		}
		
		global $wp_query;
		$wp_query = new WP_Query($args);
		
		if ($wp_query->have_posts()) :
			while (have_posts()) : the_post();
				if ($post_type == 'event') {
					get_template_part('ppp-templates/events/event', 'teaser');
				} else {
					get_template_part('template-parts/content-teaser-news');
				}
			endwhile;
			the_posts_pagination(array('mid_size' => 2));

		endif;
	endif;
		?>

		<?php
		// Historic section - only show if not filtering by date AND not for noticies
		if (empty($date_filter) && $content_type != 'noticies'):
		?>
			<h2 class="section_title"><?php _e("Històric","ppp")?></h2>

			<?php

			$paged=( get_query_var( 'paged' ) ) ? get_query_var( 'paged' ) : 1;

			// Query for historic items
			$args = array(
				'post_type' => $post_type,
				'posts_per_page' => 5,
				'paged' => $paged
			);

			// Add category filter for cursos
			if ($content_type == 'cursos') {
				$args['tax_query'] = array(
					array(
						'taxonomy' => 'category',
						'field' => 'slug',
						'terms' => 'curs',
						'operator' => 'IN',
					),
				);
			}

			// Use ppp_start_date for events (agenda and cursos), publication date for posts (noticies)
			if ($post_type == 'event') {
				$args['meta_key'] = PIMPAMPUM_PREFIX.'start_date';
				$args['orderby'] = 'meta_value_num';
				$args['order'] = 'DESC';

				// If date filter is provided, filter by that month, otherwise show past events
				if (!empty($date_filter) && $start_date && $end_date) {
					$args['meta_query'] = array(
						array(
							'key' => PIMPAMPUM_PREFIX.'start_date',
							'value' => array($start_date, $end_date),
							'compare' => 'BETWEEN',
							'type' => 'NUMERIC'
						)
					);
				} else {
					$args['meta_query'] = array(
						array(
							'key' => PIMPAMPUM_PREFIX.'start_date',
							'value' => $now,
							'compare' => '<', // Only show events with start date in the past
							'type' => 'NUMERIC'
						)
					);
				}
			} else {
				// For posts (noticies), use publication date
				$args['orderby'] = 'date';
				$args['order'] = 'DESC';

				// If date filter is provided, filter by that month, otherwise show past posts
				if (!empty($date_filter) && $start_date && $end_date) {
					$args['date_query'] = array(
						array(
							'after' => date('Y-m-d', $start_date),
							'before' => date('Y-m-d', $end_date),
							'inclusive' => true,
						)
					);
				} else {
					$args['meta_query'] = array(
						array(
							'key' => 'post_date',
							'value' => date('Y-m-d H:i:s', $now),
							'compare' => '<',
							'type' => 'DATETIME'
						)
					);
				}
			}

			$the_query = new WP_Query( $args );

			if ( $the_query->have_posts() ) {
			while ( $the_query->have_posts() ) {
				$the_query->the_post();
				get_template_part('ppp-templates/events/event', 'teaser');

			}
			}

			 // Use the specific query for pagination
			 echo paginate_links(array(
				'base' => get_pagenum_link(1) . '%_%',
				'format' => '?paged=%#%',
				'current' => max(1, $paged),
				'total' => $the_query->max_num_pages,
				'prev_text' => __('« Anterior', 'ppp'),
				'next_text' => __('Següent »', 'ppp'),
				'mid_size' => 2
			));


			?>

			<?php the_posts_pagination(array('mid_size' => 2)); ?>

		<?php endif; // End historic section ?>

			</div>

            <aside id="news_sidebar" class="sidebar">
                    <div class="caixa_sidebar">



           

                    <h2>Arxiu</h2>
                <ul>
                <?php
                // Get all posts of the current post type with dates
                $args = array(
                    'post_type' => $post_type,
                    'posts_per_page' => -1,
                );

                // Add category filter for cursos
                if ($content_type == 'cursos') {
                    $args['tax_query'] = array(
                        array(
                            'taxonomy' => 'category',
                            'field' => 'slug',
                            'terms' => 'curs',
                            'operator' => 'IN',
                        ),
                    );
                }

                // Use ppp_start_date for events, publication date for posts
                if ($post_type == 'event') {
                    $args['meta_key'] = PIMPAMPUM_PREFIX.'start_date';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                } else {
                    $args['orderby'] = 'date';
                    $args['order'] = 'DESC';
                }
                
                $archive_query = new WP_Query($args);
                $months = array();
                
                // Collect all months that have content
                if ($archive_query->have_posts()) {
                    while ($archive_query->have_posts()) {
                        $archive_query->the_post();

                        // Use ppp_start_date for events, post_date for posts
                        if ($post_type == 'event') {
                            $date = get_post_meta(get_the_ID(), PIMPAMPUM_PREFIX.'start_date', true);
                        } else {
                            $date = strtotime(get_the_date('Y-m-d'));
                        }

                        if ($date) {
                            $month_year = date('Y-m', $date);
                            if (!in_array($month_year, $months)) {
                                $months[] = $month_year;
                            }
                        }
                    }
                }
                wp_reset_postdata();
                
                // Sort months in descending order
                rsort($months);
                
                // Array of Catalan month names
                $catalan_months = array(
                    '01' => 'Gener',
                    '02' => 'Febrer',
                    '03' => 'Març',
                    '04' => 'Abril',
                    '05' => 'Maig',
                    '06' => 'Juny',
                    '07' => 'Juliol',
                    '08' => 'Agost',
                    '09' => 'Setembre',
                    '10' => 'Octubre',
                    '11' => 'Novembre',
                    '12' => 'Desembre'
                );
                
                // Display the months
                foreach ($months as $month) {
                    $parts = explode('-', $month);
                    $year = $parts[0];
                    $month_num = $parts[1];
                    $month_name = $catalan_months[$month_num];
                    
                    // Current URL without query parameters
                    $current_url = home_url($current_path);
                    
                    echo '<li><a href="' . esc_url($current_url . '?date=' . $month) . '">' . $month_name . ' ' . $year . '</a></li>';
                }
                ?>
                </ul>

                </div>
            </aside>



		</div>
		</main>

<?php get_footer(); ?>
