<?php


 get_header(); 
 $vars=get_query_var("post_type");

 $date=null;
 if(isset($_GET['date'])){
	$date=$_GET['date'];
 }
 


 
 ?>

<main id="main" class="news site-main wrap" role="main">
<p class="section_title">
  <?php 
  if($vars=="event"){
    _e("Agenda","ppp");
    
    // If filtering by date, display the month in Catalan
    if ($date) {
        $date_parts = explode('-', $date);
        $year = $date_parts[0];
        $month_num = intval($date_parts[1]);
        
        // Array of Catalan month names
        $catalan_months = array(
            1 => 'Gener',
            2 => 'Febrer',
            3 => 'Març',
            4 => 'Abril',
            5 => 'Maig',
            6 => 'Juny',
            7 => 'Juliol',
            8 => 'Agost',
            9 => 'Setembre',
            10 => 'Octubre',
            11 => 'Novembre',
            12 => 'Desembre'
        );
        
        // Get the Catalan month name
        $month_name = $catalan_months[$month_num];
        
        // Display the formatted date
        echo ' - ' . $month_name . ' del ' . $year;
    }
  } else {
    _e("Notícies","ppp");
  }
  ?></p>
  <div id="archive_content">
	  <div id="archive_llistat">
		<?php
		// Modify the main query for events to order by start_date
		if ($vars == "event") {
			$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
		
			$args = array(
				'post_type' => 'event',
				'meta_key' => 'ppp_start_date',
				'orderby' => 'meta_value_num',
				'order' => 'DESC',
				'paged' => $paged
			);
		
			// Filter by month if date parameter exists
			if ($date) {
				// Parse the date parameter (format: YYYY-MM)
				$date_parts = explode('-', $date);
				$year = $date_parts[0];
				$month = $date_parts[1];
				
				// Calculate the timestamp range for the specified month
				$start_date = mktime(0, 0, 0, $month, 1, $year);
				$end_date = mktime(23, 59, 59, $month + 1, 0, $year); // Last day of the month
				
				// Add meta query to filter events within the date range
				$args['meta_query'] = array(
					'relation' => 'AND',
					array(
						'key' => 'ppp_start_date',
						'value' => $start_date,
						'compare' => '>=',
						'type' => 'NUMERIC'
					),
					array(
						'key' => 'ppp_start_date',
						'value' => $end_date,
						'compare' => '<=',
						'type' => 'NUMERIC'
					)
				);
			}
		
			global $wp_query;
			$wp_query = new WP_Query($args);
		}

		while ( have_posts() ) : the_post();
			if($vars=="event"){
				get_template_part( 'ppp-templates/events/event-teaser');
			}else{
				get_template_part( 'template-parts/content-teaser-news');
			}
	        endwhile;
	        the_posts_pagination( array( 'mid_size' => 2 ) ); 
		   //get_template_part("template-parts/pagination");
			?>
	    </div>
	    
<?php get_sidebar("agenda")?>
	</div>
</main>

<?php get_footer(); ?>
