<?php

use <PERSON>mpamp<PERSON>\Pimpampum as ppp;

?>
<aside id="news_sidebar" class="sidebar">
	
<?php if(has_post_thumbnail() && get_post_type()!="projecte"): ?>
	<figure class="single_thumbnail">
		<?php the_post_thumbnail("large") ?>
		<figcaption><?php ppp::the_caption()?></figcaption>
	</figure>
<?php endif?>

<div class="caixa_sidebar">



	         <?php dynamic_sidebar("news")  ?>

			 

					 <p class="historic"><a href="https://www.antropologia.cat/estatic/noticies.html">Històric de notícies</a></p>

			 </div>
			   <?php
        wp_reset_query();
        ?>


    <div class="archive_mini">
        <h2><?php _e("Darreres notícíes","ppp")?></h2>
					<?php

				global $post;
				$current_post_id = $post->ID;

				$args=array(
					'post_type'=>'post',
					'posts_per_page'=>8,
					'post__not_in' => array($current_post_id), // Exclude current post
				);
				wp_reset_query();
				$the_query = new WP_Query( $args );

				if ( $the_query->have_posts() ) {
					while ( $the_query->have_posts() ) {
						$the_query->the_post();
						get_template_part("template-parts/new","teaser");

					}
				}
				?>
				</div>



	    </aside>
