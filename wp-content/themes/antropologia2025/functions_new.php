<?php

/**
 * Change the permalink structure for the event post type
 * from /event/... to /agenda/...
 */
function change_event_permalink_slug( $args, $post_type ) {
    if ( 'event' === $post_type ) {
        $args['rewrite'] = array(
            'slug'       => 'agenda',
            'with_front' => false
        );
    }
    return $args;
}
add_filter( 'register_post_type_args', 'change_event_permalink_slug', 10, 2 );


// Add excerpt support to event post type
function add_excerpt_support_for_events() {
    add_post_type_support('event', 'excerpt');
  
}
add_action('init', 'add_excerpt_support_for_events', 20);



// Register a new sidebar
function pimpampum_new_widgets_init() {
    register_sidebar( array(
        'name'          => esc_html__( 'Home Text', 'pimpampum' ),
        'id'            => 'home-text',
        'description'   => esc_html__( 'Add widgets here to appear in the home page text area.', 'pimpampum' ),
        'before_widget' => '<section id="%1$s" class="home-text widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );

    register_sidebar( array(
        'name'          => esc_html__( 'Home Publicacions', 'pimpampum' ),
        'id'            => 'home-publicacions',
        'description'   => esc_html__( 'Afegir widgets per apartat publicacions de la home.', 'pimpampum' ),
        'before_widget' => '<div id="%1$s" class="teaser pub pub-teaser widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h2 class="pub-title">',
        'after_title'   => '</h2>',
    ) );

}
add_action( 'widgets_init', 'pimpampum_new_widgets_init' );


function the_slides(){
    // Query slideshow posts
    $args = [
        'post_type' => 'slideshow',
        'posts_per_page' => -1,
        'orderby' => 'menu_order',
        'order' => 'ASC',
        'post_status' => 'publish'
    ];

    $slides_query = new WP_Query($args);

    if ($slides_query->have_posts()) :
        // Start carousel container
        echo '<section id="home-slideshow" class="home-slideshow-section">';
        echo '<div class="container">';
        echo '<div class="slides-container" style="min-height:400px;">';

        $slide_count = 0;
        $total_slides = $slides_query->post_count;

        // Debug info
        echo "<!-- Total slides: {$total_slides} -->";

        while ($slides_query->have_posts()) : $slides_query->the_post();
            $slide_count++;

            // Set up the args for the template
            global $args;
            $args = [
                'index' => $slide_count,
                'total_slides' => $total_slides
            ];

            // Include the slide template
            get_template_part('template-parts/slide');

        endwhile;

        // Navigation arrows removed as requested

        echo '</div>'; // End slides-container
        echo '</div>'; // End container
        echo '</section>'; // End home-slideshow

        // Enqueue the slideshow script
        wp_enqueue_script('home-slideshow', get_template_directory_uri() . '/js/home-slideshow.js', ['jquery'], '1.0', true);
    endif;

    wp_reset_postdata();
}


// Add the parent "Recursos" menu
function add_recursos_menu() {
    add_menu_page(
        'Recursos',
        'Recursos',
        'edit_posts',
        'recursos',
        '',
        'dashicons-portfolio',
        20
    );
}
add_action('admin_menu', 'add_recursos_menu');

// Move existing post types under "Recursos" menu
function move_post_types_under_recursos() {
    global $submenu;

    // Create the Recursos menu if it doesn't exist in the submenu array
    if (!isset($submenu['recursos'])) {
        $submenu['recursos'] = [];
    }

    // Post types to move
    $post_types = [
        'document' => 'Documents',
        'activitat' => 'Activitats',
        'publicacio' => 'Publicacions',
        'video' => 'Videos'
    ];

    // Add each post type to the Recursos submenu
    foreach ($post_types as $post_type => $label) {
        add_submenu_page(
            'recursos',
            $label,
            $label,
            'edit_posts',
            "edit.php?post_type={$post_type}"
        );

        // Remove the original menu item
        remove_menu_page("edit.php?post_type={$post_type}");
    }
}
add_action('admin_menu', 'move_post_types_under_recursos', 99);