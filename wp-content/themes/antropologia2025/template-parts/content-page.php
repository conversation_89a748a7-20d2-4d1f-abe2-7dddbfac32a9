<?php
use Pimpampum\Pimpampum as ppp;

/**
 * Template part for displaying page content in page.php.
 *
 * @link https://codex.wordpress.org/Template_Hierarchy
 *
 * @package pimpampum
 */

?>

<div id="page_submenu">

<header class="entry-header">
	<?php the_title( '<h1 class="entry-title">', '</h1>' ); ?>
</header>


<?php 

if(get_the_ID()!=181){
	$html=the_menu_if_children(get_the_ID());
	?>
	<div id="page_submenu_sidebar">
	<?php if(has_excerpt() ):?>
	<div id="excerpt">
	<?php the_excerpt()?>
	</div>
	<?php endif?>

	<?php the_post_thumbnail("large") ?>

	<ul id="sidebar_menu"><?php echo $html;?></ul>
	</div>

<?php 
	
}else{

	 if(has_excerpt() ):?>
		<div id="excerpt">
		<?php the_excerpt()?>

	

		</div>
<?php endif;
}
?>
<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>

	

		<div class="entry-content">
		
		
		<?php
		the_content();

			
		if(ppp::has_field('arxius')):
			?>

			<div class="arxius">	
			<h2>Arxius relacionats:</h2>
			<?php ppp::the_field_files("arxius");?>
			</div>

			<?php 
			endif;
	


			wp_link_pages( array(
				'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'pimpampum' ),
				'after'  => '</div>',
			) );


			//ppp::the_field_files("arxius");

		?>
	</div><!-- .entry-content -->

	<?php if ( get_edit_post_link() ) : ?>
		<footer class="entry-footer">
		<?php ppp_entry_footer(); ?>
		</footer><!-- .entry-footer -->
	<?php endif; ?>
</article><!-- #post-## -->







</div>
