<?php 
use <PERSON>mpamp<PERSON>\Pimpampum as ppp;
?>

<article class="teaser teaser_mini teaser_curs">
    <?php if (has_post_thumbnail()):?>
      <div class="teaser_mini_thumbnail">
      <?php the_post_thumbnail('foto-llistat');?>
      </div>
    <?php endif;?>

    <div class="teaser_mini_info">
    <p class="data_ppp">
        <?php $date=ppp::get_the_field("text_date");
        if(!$date){
            $date=ppp::get_the_field_timestamp("start_date");
        }
        echo $date;?>
    </p>
     <h2 class="teaser_mini_title"><a href="<?php the_permalink()?>"><?php the_title()?></a></h2>
    <div class="teaser_mini_excerpt">
    <?php 
    if (has_excerpt()) {
        echo get_the_excerpt();
    } else {
      
        print ppp::the_field("resum");
        /*
        $content = get_the_content();
        $first_sentence = strtok(strip_tags($content), '.');
        echo $first_sentence . '.';*/
    }
    ?>
    </div>
</div>
</article>