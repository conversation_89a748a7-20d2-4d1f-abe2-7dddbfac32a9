<?php

use <PERSON>mp<PERSON><PERSON>\Pimpampum as ppp;

require "config.php";
require "inscripcions.php";

add_image_size( 'teaser', 720, 400,true );
add_image_size( 'foto-llistat', 300, 200, true);




/*
function events_monthly( &$query ) {


  $post_type=get_query_var('post_type');

  if($post_type=="event"){
    if ( $query->is_main_query() && ! is_admin() ) {
      // Only modify date-based archives
      if ( is_date() ) {
        print "ok";

         $query->set('post_type',array('event'));
      }
   }
  }



}
add_action( 'pre_get_posts', 'events_monthly' );
*/


function the_event_date(){
  ?>
  <p class="data_ppp">
  <?php
  if(ppp::has_field("text_date")){

    echo ppp::get_the_field("text_date");
  }else{
      echo ppp::the_field_timestamp("start_date")?>
    <?php if(ppp::has_field("end_date")):?>
        <?php " "._e("al","ppp")." "?>  <?php echo ppp::the_field_timestamp("end_date")?>
    <?php endif;
    } ?>
  </p>
  <?php
}

function custom_menu_order($menu_ord) {
  if (!$menu_ord) return true;
  return array(
   'index.php', // this represents the dashboard link
   'edit.php?post_type=page', // this is a custom post type menu
   'edit.php',
   'edit.php?post_type=event',
   'edit.php?post_type=research',
   'edit.php?post_type=research-item',
   'edit.php?post_type=pub',
   'edit.php?post_type=destacat',
   'edit.php?post_type=slideshow', // this is the default page menu
   'edit.php?post_type=attachment', // this is the default page menu
   //'edit.php', // this is the default POST admin menu
);
}
add_filter('custom_menu_order', 'custom_menu_order');
add_filter('menu_order', 'custom_menu_order');


/**
aqui es generen les subopcions de publicacions
*/

class auto_child_page_menu
{
    /**
     * class constructor
     * <AUTHOR> Raz <<EMAIL>>
     * @param   array $args
     * @return  void
     */
    function __construct($args = array()){
        add_filter('wp_nav_menu_objects',array($this,'on_the_fly'));
    }


    /**
     * the magic function that adds the child pages
     * <AUTHOR> Raz <<EMAIL>>
     * @param  array $items
     * @return array
     */


    function on_the_fly($items) {

        global $post;
        if($post==null)return;
        
        $tmp = array();
        foreach ($items as $key => $i) {
            $tmp[] = $i;
            //if not page move on
            if ($i->object != 'page'){
                continue;
            }
            $page = get_post($i->object_id);
            //if not parent page move on
            if ( !isset($page->post_parent) || $page->post_parent != 0  ) {
                continue;
            }
            $children = get_pages( array(
              'child_of' => $i->object_id,
              'sort_order' => 'ASC',
              'sort_column' => 'menu_order'
              ) );
            foreach ((array)$children as $c) {
                //set parent menu
                $c->menu_item_parent      = $i->ID;
                $c->object_id             = $c->ID;
                $c->object                = 'page';
                $c->type                  = 'post_type';
                $c->type_label            = 'Page';
                $c->url                   = get_permalink( $c->ID);
                $c->title                 =  $c->post_title;
                $c->target                = '';
                $c->attr_title            = '';
                $c->description           = '';
                $c->classes               = array('','menu-item','menu-item-type-post_type','menu-item-object-page');
                $c->xfn                   = '';
                $c->current               = ($post->ID == $c->ID)? true: false;
                $c->current_item_ancestor = ($post->ID == $c->post_parent)? true: false; //probbably not right
                $c->current_item_parent   = ($post->ID == $c->post_parent)? true: false;
                if($c->post_parent==181 || $c->post_parent==126  ){ //hack només ica i publicacions
                  $tmp[] = $c;
                }


            }
        }
        return $tmp;
    }

  }
new auto_child_page_menu();



function my_login_logo_one() {
  ?>
  <style type="text/css">
  body{
    background:#fff;
    background-color:#fff; /* no funciona */
  }
  body.login div#login h1 a {
   background-image: url(<?php echo home_url()?>/wp-content/themes/pimpampum_theme/img/logo_ica.png);
  padding-bottom: 30px;
  }
  </style>
   <?php
}

add_action( 'login_enqueue_scripts', 'my_login_logo_one' );

function get_segment($num){
  $uri_path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
  $uri_segments = explode('/', $uri_path);
  return $uri_segments[$num];
}




//marcar el menú actual correcte
function ica_menu_item_classes( $classes, $item, $args ) {


	if( 'primary' !== $args->theme_location )
    return $classes;

  //si és fill d'una pàgina i la pàgina és la marcada
  global $post;
  if($post->post_parent!=0){
    $menu_content_id=get_post_meta( $item->ID, '_menu_item_object_id', true );
    $parent=get_post($post->post_parent);

      if($menu_content_id==$parent->ID){
        $classes[] = 'current-menu-item';
        $GLOBALS['menu_title']=$item->title;
      }


  }


  /*
  if( ( is_singular( 'post' ) || is_category() || is_tag() ) && 'Blog' == $item->title )
		$classes[] = 'current-menu-item';
	*/
	if( ( is_singular( 'research' )  ) && 'Grups de treball i recerca' == $item->title )
		$classes[] = 'current-menu-item';

  if( ( is_singular( 'event' )  ) && 'Agenda' == $item->title )
  $classes[] = 'current-menu-item';

  if( ( is_singular( 'post' )  ) && 'Notícies' == $item->title )
  $classes[] = 'current-menu-item';
    /*
	if( is_singular( 'projects' ) && 'Case Studies' == $item->title )
		$classes[] = 'current-menu-item';
		*/
	return array_unique( $classes );
}
add_filter( 'nav_menu_css_class', 'ica_menu_item_classes', 10, 3 );

function get_current_parents(){

  global $wp_query;

  $object = $wp_query->get_queried_object();

  $parent_id  = $object->post_parent;
  $parents=[];


  if($parent_id!=0) $parents[]=$parent_id;
  $depth = 0;

  while($parent_id > 0){
      $page = get_page($parent_id);
      $parent_id = $page->post_parent;
      if($parent_id!=0)
        $parents[]=$parent_id;
      $depth++;
  }

  return $parents;
}


//mostra un menu dels fills del parent i seleccion element actual
function the_menu_from_parent($parent_id,$current_id,$terms=array()){


    $grandpa=false;
  $post=get_post($current_id);
  if($post->post_parent){
    $parent=get_post($post->post_parent);
    if($parent->post_parent){
      $grandpa=true;

    }
  }
  if($grandpa) $current_id=$post->post_parent;


  $options=array(
    'child_of' => $parent_id,
    'sort_order'=>'asc',
    'depth' => 1 ,
    //'hierarchical'=>0,
    'sort_column' => 'menu_order' );

  $children = get_pages( $options );

  $term_to_pages=['27'=>195,'28'=>183,'17'=>187,'16'=>189,'40'=>185];
  $new_terms=[];
  foreach($terms as $term){
    $new_terms[]=$term_to_pages[$term];
  }
  ?>
<nav class="submenu-header">
  <ul id="sidebar_menu">

  <?php foreach($children as $child):?>
  <?php if( $child->post_parent== $parent_id):?>

  <li <?php if($child->ID==$current_id || in_array($child->ID,$new_terms)) echo "class='selected'" ?> ><a  href="<?php echo $child->guid?>"><?php echo $child->post_title?></a></li>
  <?php  endif ?>
  <?php endforeach;?>

  </ul>
  </nav>
<?php
}


function the_activitat_ica(){
  $terms=get_the_terms(get_the_ID(),"category");

	if($terms){
		foreach($terms as $term){
				if($term->slug=="activitat-ica"){
					print "<p class='activitat_ica'><span>Activitat ica</span></p>";
				}
		}
	}
}

function the_curs_ica(){
  $terms=get_the_terms(get_the_ID(),"category");

	if($terms){
		foreach($terms as $term){
				if($term->slug=="curs"){
					print "<p class='activitat_ica'><span>Curs</span></p>";
				}
		}
	}
}

//si té fills mostrar com a menu.. sino mostrar germans
function the_menu_if_children($current_id){


  $options=array(
    'child_of' => $current_id,
    'sort_order'=>'asc',
    'depth' => 1 ,
    'sort_column' => 'menu_order' );

  $children = get_pages( $options );

  if(!count($children)){
    $post=get_post($current_id);


    if( $post->post_parent==0 || $post->post_parent==181 ) return false;
    $options=array(
      'child_of' => $post->post_parent,
      'sort_order'=>'asc',
      'depth' => 1 ,
      'sort_column' => 'menu_order' );

    $children = get_pages( $options );
  }

  if(count($children)){

    ob_start();
    ?>
    <?php foreach($children as $child):?>
    <li <?php if($child->ID==$current_id) echo "class='selected'" ?> >
      <a  href="<?php echo $child->guid?>">
        <?php echo $child->post_title?></a></li>
    <?php endforeach;?>
    <?php

    $content= ob_get_contents();
    ob_end_clean();
    return $content;
  }

  return true;

}



/*

function the_ica_submenu($excerpt=true,$class="submenu",$max_depth=1){

  wp_reset_query();
    global $post;



  $parents=get_current_parents();


  if(count($parents)==0){
    $parent_id=$post->ID;
  }

  if(count($parents)==1){
    $parent_id=$post->post_parent;

  }
  if(count($parents)==2){
    $parent_id=$parents[$max_depth];

  }

  $parent=get_post($parent_id);

    $has_menu=false;


    $options=array(
      'child_of' => $parent_id,
      'sort_order'=>'asc',
      'depth' => 1 ,
      //'hierarchical'=>0,
      'sort_column' => 'menu_order' );


    $children = get_pages( $options );
   // print_r($children);
    $has_menu=true;

    if($has_menu):
      ?>
    <aside class="<?php echo $class?>">


    <?php if(has_excerpt() && $excerpt):?>
    <div id="excerpt">
    <?php the_excerpt()?>
    </div>
    <?php endif?>


    <ul id="sidebar_menu">
      <li <?php if($post->ID==$parent_id) echo "class='selected'" ?> ><a  href="<?php echo $parent->guid?>"><?php echo $parent->post_title?></a></li>
      <?php foreach($children as $child):?>
      <?php if( $child->post_parent== $parent_id):?>

      <li <?php if($child->ID==$post->ID) echo "class='selected'" ?> ><a  href="<?php echo $child->guid?>"><?php echo $child->post_title?></a></li>
      <?php endif ?>
    <?php endforeach;?>
    </ul>
      <?php
      endif;
      ?>
    </aside>


    <?php
}
*/

function get_menu_item_name( $loc ) {
  global $post;
  $locs = get_nav_menu_locations();
  $menu = wp_get_nav_menu_object( $locs[$loc] );
  $name="";
  if($menu) {
      $items = wp_get_nav_menu_items($menu->term_id);
      foreach ($items as $k => $v) {
          // Check if this menu item links to the current page
          if ($items[$k]->object_id == $post->ID) {
              $name = $items[$k]->title;
              break;
          }
      }
  }
  if($name==""){
    if($post->post_parent!=0){
      $parent=get_post($post->post_parent);
      $name=$parent->post_name;
    }

  }
  return $name;
}



add_action( 'after_setup_theme', 'ica_setup' );

function ica_setup() {
  register_nav_menu( 'intranet', __( 'Menú intranet', 'ppp' ) );
    register_nav_menu( 'upper', __( 'Menú superior', 'ppp' ) );
  register_nav_menu( 'primary', __( 'Primary Menu', 'ppp' ) );
  register_nav_menu( 'footer', __( 'Footer Menu', 'ppp' ) );
	load_theme_textdomain( 'pimpampum', get_template_directory() . '/languages' );
	add_theme_support("post-thumbnails");
}

add_filter('wp_nav_menu_items', 'add_search_form', 10, 2);
function add_search_form($items, $args) {
if( $args->theme_location == 'upper' )
        $items .= '<li class="search"><form role="search" method="get" id="searchform" action="'.home_url( '/' ).'"><input type="text" placeholder="cercar..." value="" name="s" id="s" /></form></li>';
        //<input type="submit" id="searchsubmit" value="'. esc_attr__('Search') .'" />
        return $items;
}


//canviar el nom de posts a Noticies
function ppp_change_post_label() {
  global $menu;
  global $submenu;
  $menu[5][0] = 'Noticies';
  $submenu['edit.php'][5][0] = 'Noticies';
  $submenu['edit.php'][10][0] = 'Afegir Noticies';
  $submenu['edit.php'][16][0] = 'Noticies Tags';
}
function ppp_change_post_object() {
  global $wp_post_types;
  $labels = &$wp_post_types['post']->labels;
  $labels->name = 'Noticies';
  $labels->singular_name = 'Noticies';
  $labels->add_new = 'Afegir Noticies';
  $labels->add_new_item = 'Afegir Noticies';
  $labels->edit_item = 'Editar Noticies';
  $labels->new_item = 'Noticies';
  $labels->view_item = 'Veure Noticia';
  $labels->search_items = 'Cercar Noticies';
  $labels->not_found = 'No Noticies found';
  $labels->not_found_in_trash = 'No Noticies found in Trash';
  $labels->all_items = 'All Noticies';
  $labels->menu_name = 'Noticies';
  $labels->name_admin_bar = 'Noticies';
}

add_action( 'admin_menu', 'ppp_change_post_label' );
add_action( 'init', 'ppp_change_post_object' );



function ppp_get_number_from_text($text,$num){
	$total=0;
	for($i=0;$i<strlen($text);$i++){
		$total+=ord($text[$i]);
	}
	return $total%$num;

}

function ppp_get_color_from_text($text){
  global $colors;
  $indx=ppp_get_number_from_text($text,count($colors));

  return $colors[$indx];
}



//afegir categories a event (pimpampum tools)
add_action('init','add_categories_to_event');
function add_categories_to_event(){
    register_taxonomy_for_object_type('category', 'event');
    register_taxonomy_for_object_type('research-group', 'event');
}

/** Tipus de contingut
*/



function ica_post_types(){

    $labels = array(
      'name'                  => _x( 'Publicació', 'Post Type General Name', 'ppp' ),
      'singular_name'         => _x( 'Publicació', 'Post Type Singular Name', 'ppp' ),
      'menu_name'             => __( 'Publicacions', 'ppp' ),
      'name_admin_bar'        => __( 'Publicacions', 'ppp' ),

    );
    $args = array(
      'label'                 => __( 'Publicació', 'ppp' ),
      'description'           => __( 'Post Type Description', 'ppp' ),
      'labels'                => $labels,
      'supports' => array(
               'title',
               'editor',
               'thumbnail',
               'excerpt'
           ),
      'taxonomies'            => array('category','research-group' ),
      'hierarchical'          => true,
      'public'                => true,
      'show_ui'               => true,
      'show_in_menu'          => true,
      'menu_position'         => 5,
      'menu_icon'             => 'dashicons-format-aside',
      'show_in_admin_bar'     => true,
      'show_in_nav_menus'     => true,
      'can_export'            => true,
      'has_archive'           => true,
      'exclude_from_search'   => false,
      'publicly_queryable'    => true,
      'capability_type'       => 'post',
      'yarpp_support' => true,
      'show_in_rest'          => true
    );
    register_post_type( 'pub', $args );


    	$labels = array(
    		'name'                       => _x( 'Tipus publicació', 'Taxonomy General Name', 'ppp' ),
    		'singular_name'              => _x( 'Tipus publicació', 'Taxonomy Singular Name', 'ppp' ),
    		'menu_name'                  => __( 'Tipus publicació', 'ppp' ),
    	);
    	$args = array(
    		'labels'                     => $labels,
    		'hierarchical'               => true,
    		'public'                     => true,
    		'show_ui'                    => true,
    		'show_admin_column'          => true,
    		'show_in_nav_menus'          => true,
    		'show_tagcloud'              => true,
        'yarpp_support' => true,
        'show_in_rest'          => true
    	);
    	register_taxonomy( 'pub-type', array( 'pub' ), $args );



    $labels = array(
      'name'                  => _x( 'Destacats', 'Post Type General Name', 'ppp' ),
      'singular_name'         => _x( 'Destacat', 'Post Type Singular Name', 'ppp' ),
      'menu_name'             => __( 'Destacats', 'ppp' ),
      'name_admin_bar'        => __( 'Destacats', 'ppp' ),

    );
    $args = array(
      'label'                 => __( 'Destacats', 'ppp' ),
      'description'           => __( 'Post Type Description', 'ppp' ),
      'labels'                => $labels,
      'supports' => array(
               'title',
               'editor',
               'thumbnail',
           ),
      'taxonomies'            => array( ),
      'hierarchical'          => true,
      'public'                => true,
      'show_ui'               => true,
      'show_in_menu'          => true,
      'menu_position'         => 5,
      'menu_icon'             => 'dashicons-screenoptions',
      'show_in_admin_bar'     => true,
      'show_in_nav_menus'     => true,
      'can_export'            => true,
      'has_archive'           => true,
      'exclude_from_search'   => false,
      'publicly_queryable'    => true,
      'capability_type'       => 'post',
      'yarpp_support' => true,
      'show_in_rest'          => true
    );
    register_post_type( 'destacat', $args );



    $labels = array(
      'name'                  => _x( 'Grups de recerca', 'Post Type General Name', 'ppp' ),
      'singular_name'         => _x( 'Grups de recerca', 'Post Type Singular Name', 'ppp' ),
      'menu_name'             => __( 'Grups de recerca', 'ppp' ),
      'name_admin_bar'        => __( 'Grups de recerca', 'ppp' ),

    );
    $args = array(
      'label'                 => __( 'Recerca', 'ppp' ),
      'description'           => __( 'Post Type Description', 'ppp' ),
      'labels'                => $labels,
      'supports' => array(
               'title',
               'editor',
               'thumbnail',
           ),
      'taxonomies'            => array('research-group' ),
      'hierarchical'          => true,
      'public'                => true,
      'show_ui'               => true,
      'show_in_menu'          => true,
      'menu_position'         => 5,
      'menu_icon'             => 'dashicons-welcome-learn-more',
      'show_in_admin_bar'     => true,
      'show_in_nav_menus'     => true,
      'can_export'            => true,
      'has_archive'           => true,
      'exclude_from_search'   => false,
      'publicly_queryable'    => true,
      'capability_type'       => 'post',
      'rewrite'=>array(
        'slug'=>'grups-de-treball'
      ),
      'yarpp_support' => true,
      'show_in_rest'          => true
    );
    register_post_type( 'research', $args );



    $labels = array(
      'name'                  => _x( 'Recerques', 'Post Type General Name', 'ppp' ),
      'singular_name'         => _x( 'Recerques', 'Post Type Singular Name', 'ppp' ),
      'menu_name'             => __( 'Recerques', 'ppp' ),
      'name_admin_bar'        => __( 'Recerques', 'ppp' ),

    );
    $args = array(
      'label'                 => __( 'Recerca', 'ppp' ),
      'description'           => __( 'Post Type Description', 'ppp' ),
      'labels'                => $labels,
      'supports' => array(
               'title',
               'editor',
               'thumbnail',
           ),
      'taxonomies'            => array('research-group' ),
      'hierarchical'          => true,
      'public'                => true,
      'show_ui'               => true,
      'show_in_menu'          => true,
      'menu_position'         => 5,
      'menu_icon'             => 'dashicons-welcome-learn-more',
      'show_in_admin_bar'     => true,
      'show_in_nav_menus'     => true,
      'can_export'            => true,
      'has_archive'           => true,
      'exclude_from_search'   => false,
      'publicly_queryable'    => true,
      'capability_type'       => 'post',
      'yarpp_support' => true,
      'show_in_rest'          => true
    );
    register_post_type( 'research-item', $args );



	$labels = array(
		'name'                       => _x( 'Grups de recerca', 'Taxonomy General Name', 'ppp' ),
		'singular_name'              => _x( 'Grup de recerca', 'Taxonomy Singular Name', 'ppp' ),
		'menu_name'                  => __( 'Grups de recerca', 'ppp' ),
		'all_items'                  => __( 'All Items', 'ppp' ),
		'parent_item'                => __( 'Parent Item', 'ppp' ),
		'parent_item_colon'          => __( 'Parent Item:', 'ppp' ),
		'new_item_name'              => __( 'New Item Name', 'ppp' ),
		'add_new_item'               => __( 'Add New Item', 'ppp' ),
		'edit_item'                  => __( 'Edit Item', 'ppp' ),
		'update_item'                => __( 'Update Item', 'ppp' ),
		'view_item'                  => __( 'View Item', 'ppp' ),
		'separate_items_with_commas' => __( 'Separate items with commas', 'ppp' ),
		'add_or_remove_items'        => __( 'Add or remove items', 'ppp' ),
		'choose_from_most_used'      => __( 'Choose from the most used', 'ppp' ),
		'popular_items'              => __( 'Popular Items', 'ppp' ),
		'search_items'               => __( 'Search Items', 'ppp' ),
		'not_found'                  => __( 'Not Found', 'ppp' ),
		'no_terms'                   => __( 'No items', 'ppp' ),
		'items_list'                 => __( 'Items list', 'ppp' ),
		'items_list_navigation'      => __( 'Items list navigation', 'ppp' ),
	);
	$args = array(
		'labels'                     => $labels,
		'hierarchical'               => true,
		'public'                     => true,
		'show_ui'                    => true,
		'show_admin_column'          => true,
		'show_in_nav_menus'          => true,
		'show_tagcloud'              => true,
    'yarpp_support' => true,
    'show_in_rest'          => true
	);
	register_taxonomy( 'research-group', array( 'research','pub' ), $args );


    /*
  $labels = array(
    'name'                  => _x( 'Logo', 'Post Type General Name', 'ppp' ),
    'singular_name'         => _x( 'Logo', 'Post Type Singular Name', 'ppp' ),
    'menu_name'             => __( 'Logos', 'ppp' ),
    'name_admin_bar'        => __( 'Logo', 'ppp' ),

  );
  $args = array(
    'label'                 => __( 'Logo', 'ppp' ),
    'description'           => __( 'Post Type Description', 'ppp' ),
    'labels'                => $labels,
    'supports' => array(
             'title',
             'editor',
             'thumbnail',
         ),
    'taxonomies'            => array(),
    'hierarchical'          => true,
    'public'                => true,
    'show_ui'               => true,
    'show_in_menu'          => true,
    'menu_position'         => 5,
    'menu_icon'             => 'dashicons-image-filter',
    'show_in_admin_bar'     => true,
    'show_in_nav_menus'     => true,
    'can_export'            => true,
    'has_archive'           => true,
    'exclude_from_search'   => false,
    'publicly_queryable'    => true,
    'capability_type'       => 'post',
    'yarpp_support' => true,
    'show_in_rest'          => true
  );
  register_post_type( 'logo', $args );
*/

  //vincular grups de recerca a events
  register_taxonomy_for_object_type('research-group', 'event');

}

  add_action( 'init', 'ica_post_types' );


/** definir camps especials

*/


function ica_camps_especials(){
    global $colors;


  	//camps per pàgines
    $cmb_pag= new_cmb2_box( array(
      'id'            => PIMPAMPUM_PREFIX . 'metaboxnew',
      'title'         => esc_html__( 'Camps Noticia', 'ppp' ),
      'object_types'  => array( 'page' ),
    ) );

  ppp::addCMB2Field($cmb_pag,"Arxius","arxius","file_list");
  ppp::addCMB2Field($cmb_pag,"Codi","code","text");
  $cmb_pag->add_field( array(
    'name'             => 'Mida',
    'id'               => PIMPAMPUM_PREFIX.'size',
    'type'             => 'select',
    'show_option_none' => true,
    'default'          => '0',
    'options'          => array(
      'No publicat'=>'0',
      'XL' => 'XL',
      'L'   => 'L',
      'M'     =>  'M',
      'S'     =>  'S',
    ),
  ) );



  //camps per noticies
  $cmb_news= new_cmb2_box( array(
    'id'            => PIMPAMPUM_PREFIX . 'metaboxnews',
    'title'         => esc_html__( 'Camps especials', 'ppp' ),
    'object_types'  => array( 'post' ),
  ) );
  ppp::addCMB2Field($cmb_news,"Arxius","arxius","file_list");




	//camps per destacats
  $cmb_destacat= new_cmb2_box( array(
    'id'            => PIMPAMPUM_PREFIX . 'metaboxdes',
    'title'         => esc_html__( 'Camps Banner', 'ppp' ),
    'object_types'  => array( 'destacat' ), // Post type
  ) );


  ppp::addCMB2Field($cmb_destacat,"Subtítol","subtitol","text");
  ppp::addCMB2Field($cmb_destacat,"Link","link","text_url");
  $cmb_destacat->add_field( array(
    'name'             => 'Mida',
    'id'               => PIMPAMPUM_PREFIX.'size',
    'type'             => 'select',
    'show_option_none' => true,
    'default'          => '0',
    'options'          => array(
      'No publicat'=>'0',
      'XL' => 'XL',
      'L'   => 'L',
      'M'     =>  'M',
      'S'     =>  'S',
    ),
  ) );





  	//camps per publicació
  		$cmb_publicacio= new_cmb2_box( array(
  			'id'            => PIMPAMPUM_PREFIX . 'metaboxpub',
  			'title'         => esc_html__( 'Camps Publicació', 'ppp' ),
  			'object_types'  => array( 'pub' ), // Post type
  		) );

    //ppp::addCMB2Field($cmb_publicacio,"Resum","resum","textarea");

    ppp::addCMB2Field($cmb_publicacio,"Subtítol","subtitle","text");
    ppp::addCMB2Field($cmb_publicacio,"Link","link","text_url");

   // ppp::addCMB2Field($cmb_publicacio,"Autor","author","text");
   // ppp::addCMB2Field($cmb_publicacio,"Editorial","editorial","text");
   // ppp::addCMB2Field($cmb_publicacio,"Col·lecció","collection","text");


   /*
    $cmb_publicacio->add_field( array(
      'name'             => 'Destacar a la home',
      'id'               => PIMPAMPUM_PREFIX.'size',
      'type'             => 'select',
      'show_option_none' => false,
      'default'          => '0',
      'options'          => array(
        '0'=>'No destacat',
        'XL' => 'XL',
        'L'   => 'L',
        'M'     =>  'M',
        'S'     =>  'S',
      ),
    ) );
*/

/*
    $cmb_publicacio->add_field( array(
      'name'    => 'Color',
      'id'      => PIMPAMPUM_PREFIX.'color',
      'type'    => 'colorpicker',
      'default' => 'set_color_default',
      'hide' =>true,
      'attributes' => array(
          'data-colorpicker' => json_encode( array(
              // Iris Options set here as values in the 'data-colorpicker' array
              'palettes' => $colors,
          ) ),
      ),
  ) );*/


    //camps per recerca
      $cmb_recerca= new_cmb2_box( array(
        'id'            => PIMPAMPUM_PREFIX . 'metaboxresearch',
        'title'         => esc_html__( 'Camps Grups de Recerca', 'ppp' ),
        'object_types'  => array( 'research' ), // Post type
      ) );




    //ppp::addCMB2Field($cmb_publicacio,"Logo","logo","file");
    /*
    ppp::addCMB2Field($cmb_publicacio,"Objectius","objectius","wysiwyg");
    ppp::addCMB2Field($cmb_publicacio,"Metodologia","metodologia","wysiwyg");
    ppp::addCMB2Field($cmb_publicacio,"Temes","temes","wysiwyg");
    ppp::addCMB2Field($cmb_publicacio,"Objectius","objectius","wysiwyg");
*/

    $group_field_id = $cmb_recerca->add_field( array(
      'id'          => PIMPAMPUM_PREFIX.'recerca_repeat_group',
      'type'        => 'group',
      'description' => __( 'Es poden afegir els camps que calgui', 'cmb2' ),
      // 'repeatable'  => false, // use false if you want non-repeatable group
      'options'     => array(
        'group_title'       => __( 'Entrada {#}', 'cmb2' ), // since version 1.1.4, {#} gets replaced by row number
        'add_button'        => __( 'Afegir una altra entrada', 'cmb2' ),
        'remove_button'     => __( 'Eliminar entrada', 'cmb2' ),
        'sortable'          => true,
        // 'closed'         => true, // true to have the groups closed by default
        // 'remove_confirm' => esc_html__( 'Are you sure you want to remove?', 'cmb2' ), // Performs confirmation before removing group.
      ),
    ) );

    // Id's for group's fields only need to be unique for the group. Prefix is not needed.
    $cmb_recerca->add_group_field( $group_field_id, array(
      'name' => 'Títol',
      'id'   => 'title',
      'type' => 'text',
      // 'repeatable' => true, // Repeatable fields are supported w/in repeatable groups (for most types)
    ) );

    $cmb_recerca->add_group_field( $group_field_id, array(
      'name' => 'Descripció',
      'description' => 'Escriu el contingut',
      'id'   => 'description',
      'type' => 'wysiwyg',
    ) );






    ppp::addCMB2Field($cmb_recerca,"Memòries","memories","file_list");

    ppp::addCMB2Field($cmb_recerca,"Coordinació","coordinacio","textarea");
   // ppp::addCMB2Field($cmb_recerca,"Membres del grup","membres","textarea");
    ppp::addCMB2Field($cmb_recerca,"Correus de contacte","correus","textarea","Cadascun a una línia");
    ppp::addCMB2Field($cmb_recerca,"Web","link","text_url");

        //camps per recerca
        $cmb_recerca_item= new_cmb2_box( array(
          'id'            => PIMPAMPUM_PREFIX . 'metaboxresearchitem',
          'title'         => esc_html__( 'Camps Recerca', 'ppp' ),
          'object_types'  => array( 'research-item' ), // Post type
        ) );
        ppp::addCMB2Field($cmb_recerca_item,"Equip investigador","authors","textarea");
        ppp::addCMB2Field($cmb_recerca_item,"Any","any","text");
        ppp::addCMB2Field($cmb_recerca_item,"Amb el suport de","suport","textarea");
        ppp::addCMB2Field($cmb_recerca_item,"Més informació","link","textarea", "Un link a cada linia");


/*

    $group_field_id = $cmb_publicacio->add_field( array(
	'id'          => 'wiki_test_repeat_group',
	'type'        => 'group',
	'description' => __( 'Generates reusable form entries', 'cmb2' ),
	// 'repeatable'  => false, // use false if you want non-repeatable group
	'options'     => array(
		'group_title'       => __( 'Entry {#}', 'cmb2' ), // since version 1.1.4, {#} gets replaced by row number
		'add_button'        => __( 'Add Another Entry', 'cmb2' ),
		'remove_button'     => __( 'Remove Entry', 'cmb2' ),
		'sortable'          => true,
		// 'closed'         => true, // true to have the groups closed by default
		// 'remove_confirm' => esc_html__( 'Are you sure you want to remove?', 'cmb2' ), // Performs confirmation before removing group.
	),
) );

// Id's for group's fields only need to be unique for the group. Prefix is not needed.
$cmb_publicacio->add_group_field( $group_field_id, array(
	'name' => 'Entry Title',
	'id'   => 'title',
	'type' => 'text',
	// 'repeatable' => true, // Repeatable fields are supported w/in repeatable groups (for most types)
) );

$cmb_publicacio->add_group_field( $group_field_id, array(
	'name' => 'Description',
	'description' => 'Write a short description for this entry',
	'id'   => 'description',
	'type' => 'textarea_small',
) );

$cmb_publicacio->add_group_field( $group_field_id, array(
	'name' => 'Entry Image',
	'id'   => 'image',
	'type' => 'file',
) );

$cmb_publicacio->add_group_field( $group_field_id, array(
	'name' => 'Image Caption',
	'id'   => 'image_caption',
	'type' => 'text',
) );
*/


}

add_action( 'cmb2_admin_init','ica_camps_especials' );


/*
function the_page_link($slug){
  echo get_the_permalink(pll_get_post(get_page_by_path( $slug )->ID));
}


function ppp_page_is_child($id){
	global $post;
	if(!empty($post->ancestors)){
		if(in_array($id,$post->ancestors)){
			return true;
		}
	}
	if($post->ID==$id){
		return true;
	}
	return false;
}
*/





/** sidebars */
register_sidebar( array(
 'name' => 'Home',
 'id' => 'home',
 'before_widget' => '<div id="%1$s" class="widget %2$s">',
	'after_widget'  => '</div>',
 ) );

 /*
 register_sidebar( array(
  'name' => 'Noticies',
  'id' => 'news',
  'before_widget' => '<div id="%1$s" class="widget %2$s">',
   'after_widget'  => '</div>',
  ) );
  register_sidebar( array(
    'name' => 'Agenda',
    'id' => 'agenda',
    'before_widget' => '<div id="%1$s" class="widget %2$s">',
     'after_widget'  => '</div>',
    ) );

*/


register_sidebar( array(
 'name' => 'Footer',
 'id' => 'footer',
 'before_widget' => '<div id="%1$s" class="widget %2$s">',
	'after_widget'  => '</div>',
 ) );





/**
scripts y css
*/

function ppp_scripts() {
  wp_enqueue_style( 'menu-responsive-css-theme', get_template_directory_uri().'/css/menu-responsive.css', array(), '', 'all' );

	wp_enqueue_script( 'addthis', '//s7.addthis.com/js/300/addthis_widget.js#pubid=danilon');
    wp_enqueue_script( 'ppp', get_template_directory_uri() . '/js/ppp.js', array('jquery') );
  //wp_enqueue_script( 'autogrow', get_template_directory_uri() . '/js/jquery.autogrow-textarea.js', array('jquery') );

}
add_action( 'wp_enqueue_scripts', 'ppp_scripts' );


function set_color_default( $field_args, $field ) {
  global $colors;
  $post=get_post($field->object_id);
  $color = $colors[array_rand($colors)];
  return $color;
}

$role = get_role('editor');
$role->add_cap('edit_theme_options');


 /** WordPress: Remove unwonted image sizes */

add_filter('intermediate_image_sizes', function($sizes) {
  return array_diff($sizes, ['medium_large', 'yarpp-thumbnail']);  // Medium Large (768 x 0) and yarpp-thumbnail (120 x 120)
});

add_action( 'init', 'j0e_remove_large_image_sizes' );
function j0e_remove_large_image_sizes() {
  remove_image_size( '1536x1536' );             
  remove_image_size( '2048x2048' );

}

