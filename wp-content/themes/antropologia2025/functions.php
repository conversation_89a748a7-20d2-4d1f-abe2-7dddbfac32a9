<?php
/**
 * pimpampum functions and definitions.
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package pimpampum
 */


require "functions_ppp.php";
require "functions_experts.php";
require "functions_new.php";

if ( ! function_exists( 'pimpampum_setup' ) ) :
/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function pimpampum_setup() {
	/*
	 * Make theme available for translation.
	 * Translations can be filed in the /languages/ directory.
	 * If you're building a theme based on pimpampum, use a find and replace
	 * to change 'pimpampum' to the name of your theme in all the template files.
	 */
	load_theme_textdomain( 'pimpampum', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
	 * Let WordPress manage the document title.
	 * By adding theme support, we declare that this theme does not use a
	 * hard-coded <title> tag in the document head, and expect WordPress to
	 * provide it for us.
	 */
	add_theme_support( 'title-tag' );

	/*
	 * Enable support for Post Thumbnails on posts and pages.
	 *
	 * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
	 */
	add_theme_support( 'post-thumbnails' );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus( array(
		'primary' => esc_html__( 'Primary', 'pimpampum' ),
		'secondary' => esc_html__( 'Lateral', 'pimpampum' ),
		'preheader' => esc_html__( 'Preheader', 'pimpampum' ),
	) );

	/*
	 * Switch default core markup for search form, comment form, and comments
	 * to output valid HTML5.
	 */
	add_theme_support( 'html5', array(
		'search-form',
		'comment-form',
		'comment-list',
		'gallery',
		'caption',
	) );

	// Set up the WordPress core custom background feature.
	add_theme_support( 'custom-background', apply_filters( 'pimpampum_custom_background_args', array(
		'default-color' => 'ffffff',
		'default-image' => '',
	) ) );
}
endif;
add_action( 'after_setup_theme', 'pimpampum_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function pimpampum_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'pimpampum_content_width', 1000 );
}
add_action( 'after_setup_theme', 'pimpampum_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function pimpampum_widgets_init() {

	/*
	register_sidebar( array(
		'name'          => esc_html__( 'Sidebar', 'pimpampum' ),
		'id'            => 'sidebar-1',
		'description'   => esc_html__( 'Add widgets here.', 'pimpampum' ),
		'before_widget' => '<section id="%1$s" class="widget %2$s">',
		'after_widget'  => '</section>',
		'before_title'  => '<h2 class="widget-title">',
		'after_title'   => '</h2>',
	) );
	 */
}
add_action( 'widgets_init', 'pimpampum_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function pimpampum_scripts() {
	wp_enqueue_style( 'pimpampum-style', get_stylesheet_uri() );

	// Enqueue extra styles
	wp_enqueue_style( 'pimpampum-extra-styles', get_template_directory_uri() . '/extra.css', array(), '1.0.0' );

	// Enqueue document styles
	wp_enqueue_style( 'pimpampum-document-styles', get_template_directory_uri() . '/css/document-styles.css', array(), '1.0.0' );

	// Enqueue publication styles
	wp_enqueue_style( 'pimpampum-publicacio-styles', get_template_directory_uri() . '/css/publicacio-styles.css', array(), '1.0.0' );

	// Enqueue video styles
	wp_enqueue_style( 'pimpampum-video-styles', get_template_directory_uri() . '/css/video-styles.css', array(), '1.0.0' );

	// Enqueue activity styles
	wp_enqueue_style( 'pimpampum-activitat-styles', get_template_directory_uri() . '/css/activitat-styles.css', array(), '1.0.0' );

	// Enqueue Thickbox for video popups
	wp_enqueue_style( 'thickbox' );
	wp_enqueue_script( 'thickbox' );

	// Enqueue video teaser script
	wp_enqueue_script( 'pimpampum-video-teaser', get_template_directory_uri() . '/js/video-teaser.js', array('jquery', 'thickbox'), '1.0.0', true );

	// Register masonry as a dependency for other scripts
	wp_register_script('masonry', 'https://unpkg.com/masonry-layout@4/dist/masonry.pkgd.min.js', array('jquery'), '4.2.2', true);

//	wp_enqueue_script( 'pimpampum-navigation', get_template_directory_uri() . '/js/navigation.js', array(), '20151215', true );

	wp_enqueue_script( 'pimpampum-skip-link-focus-fix', get_template_directory_uri() . '/js/skip-link-focus-fix.js', array(), '20151215', true );

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', 'pimpampum_scripts' );

/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Custom functions that act independently of the theme templates.
 */
require get_template_directory() . '/inc/extras.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
require get_template_directory() . '/inc/jetpack.php';

add_action( 'init', 'my_add_excerpts_to_pages' );
function my_add_excerpts_to_pages() {
     add_post_type_support( 'page', 'excerpt' );
}
?>